"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/logbook/trip-log.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripLog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/eventType */ \"(app-pages-browser)/./src/app/offline/models/eventType.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _depart_time__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./depart-time */ \"(app-pages-browser)/./src/app/ui/logbook/depart-time.tsx\");\n/* harmony import */ var _exp_arrival_time__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./exp-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/exp-arrival-time.tsx\");\n/* harmony import */ var _actual_arrival_time__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./actual-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./events */ \"(app-pages-browser)/./src/app/ui/logbook/events.tsx\");\n/* harmony import */ var _trip_log_pob__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./trip-log-pob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx\");\n/* harmony import */ var _trip_log_vob__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./trip-log-vob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-vob.tsx\");\n/* harmony import */ var _trip_log_dgr__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./trip-log-dgr */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-dgr.tsx\");\n/* harmony import */ var _components_trip_comments__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./components/trip-comments */ \"(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_master__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./components/master */ \"(app-pages-browser)/./src/app/ui/logbook/components/master.tsx\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React and Next.js imports\n\n\n\n\n\n\n// Utility imports\n\n\n\n\n\n\n\n// Model imports\n\n\n\n\n\n// UI Component imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Reusable TripReportAccordionContent component\nconst TripReportAccordionContent = (param)=>{\n    let { currentTrip, offline, tripReport, updateTripReport, updateTripReport_LogBookEntrySection, currentTripRef, locations, locked, edit_tripReport, vessel, crewMembers, logBookConfig, client, canCarryVehicles, canCarryDangerousGoods, selectedDGR, displayDangerousGoods, setDisplayDangerousGoods, displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing, allDangerousGoods, setAllDangerousGoods, logBookStartDate, masterID, vessels, setSelectedRowEvent, setCurrentEventTypeEvent, setCurrentStopEvent, currentEventTypeEvent, currentStopEvent, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, comment, setComment, displayFieldTripLog, signatureKey, signature, setSignature, handleCancel, handleSave } = param;\n    var _currentTripRef_current;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"space-y-6\", locked || !edit_tripReport ? \"opacity-70 pointer-events-none\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col inset-y-0 items-center pt-10 pb-4 absolute -left-5 sm:-left-[25px] min-h-24 w-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"border-l h-full border-wedgewood-200 border-dashed\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_depart_time__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        templateStyle: \"\",\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                        label: \"Departure location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLocation)=>{\n                                // Update the from location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"fromLocationID\",\n                                        value: selectedLocation.value,\n                                        label: selectedLocation.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                fromLocationID: selectedLocation.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_18__.H5, {\n                        children: \"PEOPLE ON BOARD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_pob__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        vessel: vessel,\n                        crewMembers: crewMembers,\n                        logBookConfig: logBookConfig,\n                        masterTerm: client === null || client === void 0 ? void 0 : client.masterTerm,\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            canCarryVehicles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_vob__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                offline: offline,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 25\n                            }, undefined),\n                            canCarryDangerousGoods && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_dgr__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                offline: offline,\n                                locked: locked || !edit_tripReport,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig,\n                                selectedDGR: selectedDGR,\n                                members: crewMembers,\n                                displayDangerousGoods: displayDangerousGoods,\n                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                allDangerousGoods: allDangerousGoods,\n                                setAllDangerousGoods: setAllDangerousGoods\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_events__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        offline: offline,\n                        logBookStartDate: logBookStartDate,\n                        currentTrip: currentTrip,\n                        logBookConfig: logBookConfig,\n                        updateTripReport: updateTripReport,\n                        locked: locked,\n                        geoLocations: locations,\n                        tripReport: tripReport,\n                        crewMembers: crewMembers,\n                        masterID: masterID,\n                        vessel: vessel,\n                        vessels: vessels,\n                        setSelectedRow: setSelectedRowEvent,\n                        setCurrentEventType: setCurrentEventTypeEvent,\n                        setCurrentStop: setCurrentStopEvent,\n                        currentEventType: currentEventTypeEvent,\n                        currentStop: currentStopEvent,\n                        tripReport_Stops: tripReport_Stops,\n                        setTripReport_Stops: setTripReport_Stops,\n                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                        selectedDGRPVPD: selectedDGRPVPD,\n                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                        fuelLogs: fuelLogs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                        label: \"Arrival location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLoc)=>{\n                                // Update the to location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"toLocationID\",\n                                        value: selectedLoc.value,\n                                        label: selectedLoc.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                toLocationID: selectedLoc.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 131,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_exp_arrival_time__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 340,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_actual_arrival_time__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 347,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trip_comments__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                setCommentField: setComment,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 354,\n                columnNumber: 13\n            }, undefined),\n            displayFieldTripLog(\"MasterID\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked || !edit_tripReport ? \"pointer-events-none\" : \"\", \" max-w-sm\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_master__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                    offline: offline,\n                    currentTrip: currentTrip,\n                    tripReport: tripReport,\n                    crewMembers: crewMembers,\n                    updateTripReport: updateTripReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 362,\n                columnNumber: 17\n            }, undefined),\n            displayFieldTripLog(\"Signature\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                locked: locked,\n                title: \"Signature Confirmation\",\n                description: \"By signing below, I confirm that the recorded entries are accurate to the best of my knowledge and in accordance with the vessel's operating procedures and regulations.\",\n                signature: currentTripRef === null || currentTripRef === void 0 ? void 0 : (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.sectionSignature,\n                onSignatureChanged: (sign)=>{\n                    setSignature(sign);\n                }\n            }, \"\".concat(signatureKey, \"-\").concat(currentTrip.id), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 375,\n                columnNumber: 17\n            }, undefined),\n            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_36__.FormFooter, {\n                className: \"justify-end flex-nowrap xs:gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        variant: \"back\",\n                        onClick: handleCancel,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        iconLeft: _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"],\n                        onClick: handleSave,\n                        children: \"Update\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 388,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n        lineNumber: 124,\n        columnNumber: 9\n    }, undefined);\n};\n_c = TripReportAccordionContent;\nfunction TripLog(param) {\n    let { tripReport = false, logBookConfig, updateTripReport, locked, crewMembers, masterID, createdTab = false, setCreatedTab, currentTrip = false, setCurrentTrip, vessels, offline = false, fuelLogs, logBookStartDate } = param;\n    var _tripReport_find;\n    _s();\n    // const router = useRouter()\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const allowedVesselTypes = [\n        \"SLALL\",\n        \"Tug_Boat\",\n        \"Passenger_Ferry\",\n        \"Water_Taxi\"\n    ];\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_40__.useQueryState)(\"tab\", {\n        defaultValue: \"crew\"\n    });\n    const [tabTSS, setTabTSS] = (0,nuqs__WEBPACK_IMPORTED_MODULE_40__.useQueryState)(\"tss\", {\n        defaultValue: \"\"\n    });\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eventTypes, setEventTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openTripModal, setOpenTripModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bufferTripID, setBufferTripID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDGR, setSelectedDGR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoods, setDisplayDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [openEventModal, setOpenEventModal] = useState(false)\n    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)\n    const [tripReport_Stops, setTripReport_Stops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDGRPVPD, setSelectedDGRPVPD] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRowEvent, setSelectedRowEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [riskBufferEvDgr, setRiskBufferEvDgr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDangerousGoods, setAllDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEventTypeEvent, setCurrentEventTypeEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStopEvent, setCurrentStopEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signatureKey, setSignatureKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default()());\n    const currentTripRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [deleteTripItem, setDeleteTripItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteTripConfirmation, setDeleteTripConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canCarryDangerousGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesDangerousGoods);\n    }, [\n        vessel\n    ]);\n    const canCarryVehicles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesVehicles);\n    }, [\n        vessel\n    ]);\n    // Initialize client\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_10__.getOneClient)(setClient);\n    }\n    // Update signature state when currentTrip changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentTrip && currentTrip.sectionSignature) {\n            setSignature(currentTrip.sectionSignature.signatureData || \"\");\n        } else {\n            setSignature(\"\");\n        }\n    }, [\n        currentTrip\n    ]);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(tripReport ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.comment : \"\");\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_tripReport, setEdit_tripReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const clientModel = new _app_offline_models_client__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const eventTypeModel = new _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_16__[\"default\"]();\n    const [openTripSelectionDialog, setOpenTripSelectionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tripReportSchedules, setTripReportSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripReportSchedule, setSelectedTripReportSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tripScheduleServices, setTripScheduleServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNextTrips, setShowNextTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreatingScheduledTrip, setIsCreatingScheduledTrip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPastScheduledTrips, setShowPastScheduledTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"EDIT_LOGBOOKENTRY_TRIPREPORT\", permissions)) {\n                setEdit_tripReport(true);\n            } else {\n                setEdit_tripReport(false);\n            }\n        }\n    };\n    const offlineLoad = async ()=>{\n        const locations = await geoLocationModel.getAll();\n        setLocations(locations);\n        const types = await eventTypeModel.getAll();\n        setEventTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.getPermissions);\n        loadTripScheduleServices();\n        if (!locations) {\n            if (offline) {\n                offlineLoad();\n            } else {\n                loadLocations();\n                loadEventTypes();\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (createdTab) {\n            setSelectedTab(createdTab);\n        }\n    }, [\n        createdTab\n    ]);\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_10__.getVesselByID)(+vesselID, setVessel);\n    }\n    const scrollToAccordionItem = (tripId)=>{\n        const element = document.getElementById(\"triplog-\".concat(tripId));\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport && currentTrip) {\n            const trip = tripReport.find((trip)=>trip.id === currentTrip.id);\n            currentTripRef.current = trip;\n            setCurrentTrip(trip);\n        }\n        if (tripReport && bufferTripID > 0) {\n            const trip = tripReport.find((trip)=>trip.id === bufferTripID);\n            if (trip) {\n                currentTripRef.current = trip;\n                setCurrentTrip(trip);\n                // Only expand accordion and scroll for regular trips, not scheduled trips\n                if (!selectedTripReportSchedule) {\n                    setAccordionValue(trip.id.toString());\n                    scrollToAccordionItem(trip.id);\n                }\n                setOpenTripModal(true);\n                setSelectedTab(trip.id);\n                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default()());\n                // Initialize signature data if available\n                if (trip.sectionSignature) {\n                    setSignature(trip.sectionSignature.signatureData || \"\");\n                } else {\n                    setSignature(\"\");\n                }\n                // Initialize trip-specific state\n                setRiskBufferEvDgr(trip === null || trip === void 0 ? void 0 : trip.dangerousGoodsChecklist);\n                setOpenTripStartRiskAnalysis(false);\n                setAllDangerousGoods(false);\n                setCurrentStopEvent(false);\n                setCurrentEventTypeEvent(false);\n                setSelectedRowEvent(false);\n                setDisplayDangerousGoods((trip === null || trip === void 0 ? void 0 : trip.enableDGR) === true);\n                setDisplayDangerousGoodsSailing((trip === null || trip === void 0 ? void 0 : trip.designatedDangerousGoodsSailing) === true);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setAllPVPDDangerousGoods(false);\n                setSelectedDGRPVPD(false);\n                setTripReport_Stops(false);\n            }\n            setBufferTripID(0);\n        }\n    }, [\n        tripReport\n    ]);\n    const [loadLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setLocations(response.readGeoLocations.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading locations\", error);\n        }\n    });\n    const [loadEventTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_EVENT_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setEventTypes(response.readEventTypes.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading activity types\", error);\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_Stop, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const handleCreateTripReportScheduleStops = async (logBookEntrySectionID)=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(selectedTripReportSchedule)) {\n            const tripStops = selectedTripReportSchedule.tripReportScheduleStops.nodes || [];\n            await Promise.all(tripStops.map(async (stop)=>{\n                const input = {\n                    logBookEntrySectionID: logBookEntrySectionID,\n                    tripReportScheduleStopID: stop.id,\n                    arriveTime: stop.arriveTime,\n                    departTime: stop.departTime,\n                    stopLocationID: stop.stopLocationID\n                };\n                await createTripReport_Stop({\n                    variables: {\n                        input: input\n                    }\n                });\n            }));\n            setSelectedTripReportSchedule(null);\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        logBookEntrySectionID\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        logBookEntrySectionID\n                    ]\n                });\n            }\n        }\n    };\n    const [createTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            handleCreateTripReportScheduleStops(data.id);\n            // Always set accordion value and scroll for newly created trips\n            setAccordionValue(data.id.toString());\n            scrollToAccordionItem(data.id);\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n            setCurrentTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [readTripReportSchedules, { loading: readTripReportSchedulesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripReportSchedules, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripReportSchedules.nodes.filter((trip)=>// only show trips for the current vessel\n                trip.vehicles.nodes.some((vehicle)=>+vehicle.id === +vesselID));\n            if (showNextTrips) {\n                // only show 1 past trip and 4 upcoming trips\n                const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n                const pastIndex = data.findIndex((trip)=>trip.departTime >= currentTime);\n                const result = (pastIndex > 0 ? [\n                    data[pastIndex - 1]\n                ] : []).concat(data.slice(pastIndex, pastIndex + 4));\n                setTripReportSchedules(result);\n            } else {\n                setTripReportSchedules(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripReportSchedules\", error);\n        }\n    });\n    const [readTripScheduleServices] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripScheduleServices, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripScheduleServices.nodes.map((tss)=>{\n                return {\n                    label: tss.title,\n                    value: tss.id\n                };\n            });\n            setTripScheduleServices(data);\n            setTripReportSchedules([]);\n        // setOpenTripSelectionDialog(true)\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripScheduleServices\", error);\n        }\n    });\n    const loadTripScheduleServices = async ()=>{\n        await readTripScheduleServices({\n            variables: {\n                filter: {\n                    vehicles: {\n                        id: {\n                            eq: vesselID\n                        }\n                    }\n                }\n            }\n        });\n    };\n    const loadTripReportSchedules = async (tripScheduleServiceID)=>{\n        setTripReportSchedules([]);\n        await readTripReportSchedules({\n            variables: {\n                filter: {\n                    // archived: { eq: false },\n                    // start: { eq: logBookStartDate },\n                    tripScheduleServiceID: {\n                        eq: tripScheduleServiceID\n                    }\n                }\n            }\n        });\n    };\n    const doCreateTripReport = async function(input) {\n        let isScheduledTrip = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!edit_tripReport) {\n            sonner__WEBPACK_IMPORTED_MODULE_17__.toast.error(\"You do not have permission to add a trip\");\n            return;\n        }\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setIsCreatingScheduledTrip(isScheduledTrip);\n        if (offline) {\n            const data = await tripReportModel.save({\n                ...input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_11__.generateUniqueId)()\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            // For offline mode, immediately set accordion value since the trip is created synchronously\n            setAccordionValue(data.id.toString());\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        } else {\n            createTripReport_LogBookEntrySection({\n                variables: {\n                    input: input\n                }\n            });\n        }\n        setRiskBufferEvDgr(false);\n        setOpenTripStartRiskAnalysis(false);\n        setAllDangerousGoods(false);\n        setCurrentStopEvent(false);\n        setCurrentEventTypeEvent(false);\n        setSelectedRowEvent(false);\n        setDisplayDangerousGoods(false);\n        setDisplayDangerousGoodsSailing(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        setAllPVPDDangerousGoods(false);\n        setSelectedDGRPVPD(false);\n        setTripReport_Stops(false);\n    };\n    const handleCustomTrip = ()=>{\n        setOpenTripSelectionDialog(false);\n        const input = {\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, false) // false indicates this is a regular trip\n        ;\n    };\n    // Removed unused handleEditTrip function\n    const [createLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: +data.logBookEntrySectionID,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error saving signature\", error);\n        }\n    });\n    const [updateLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            var _currentTripRef_current;\n            const data = response.updateLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"341 TripLog updateLogBookEntrySection_Signature\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        // Log signature information for debugging\n        // Ensure we have a valid signature\n        const sigVariables = {\n            logBookEntrySectionID: currentTrip.id,\n            memberID: localStorage.getItem(\"userId\"),\n            signatureData: signature || \"\"\n        };\n        if (+currentTrip.sectionSignatureID > 0) {\n            // Update signature\n            updateLogBookEntrySection_Signature({\n                variables: {\n                    input: {\n                        ...sigVariables,\n                        id: +currentTrip.sectionSignatureID\n                    }\n                }\n            });\n        } else {\n            // Create signature\n            createLogBookEntrySection_Signature({\n                variables: {\n                    input: sigVariables !== null && sigVariables !== void 0 ? sigVariables : \"\"\n                }\n            });\n        }\n        if (offline) {\n            // updateTripReport_LogBookEntrySection\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                comment: comment || null\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n        } else {\n            var _currentTripRef_current;\n            await updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        comment: comment || null\n                    }\n                }\n            });\n            setOpenTripModal(false);\n            setCurrentTrip(false);\n        }\n    };\n    const displayFieldTripLog = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"TripReport_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const convertTimeFormat = (time)=>{\n        if (time === null || time === undefined) return \"\";\n        const [hours, minutes] = time.split(\":\");\n        return \"\".concat(hours, \":\").concat(minutes);\n    };\n    const handleCancel = ()=>{\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setAccordionValue(\"\");\n    };\n    // Removed unused functions\n    const initOffline = async ()=>{\n        var _localStorage_getItem;\n        // getOneClient\n        const client = await clientModel.getById((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n        setClient(client);\n        // getVesselByID(+vesselID, setVessel)\n        const vessel = await vesselModel.getById(vesselID);\n        setVessel(vessel);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            initOffline();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_7___default()(tabTSS))) {\n            loadTripReportSchedules(tabTSS);\n        }\n    }, [\n        tabTSS\n    ]);\n    /**\r\n     * Takes an array of trip report schedules and returns an array of the latest unique schedules based on:\r\n     * - departTime\r\n     * - arriveTime\r\n     * - fromLocationID\r\n     * - toLocationID\r\n     * - start\r\n     *\r\n     * Schedules with the same combination of the above properties will be deduplicated, with the latest schedule (based on id) being kept.\r\n     *\r\n     * @param {TripReportSchedule[]} schedules - An array of trip report schedules\r\n     * @returns {TripReportSchedule[]} - An array of the latest unique trip report schedules\r\n     */ const getLatestUniqueSchedules = (schedules)=>{\n        const map = new Map();\n        for (const schedule of schedules){\n            const key = \"\".concat(schedule.departTime, \"|\").concat(schedule.arriveTime, \"|\").concat(schedule.fromLocationID, \"|\").concat(schedule.toLocationID, \"|\").concat(schedule.start);\n            const existing = map.get(key);\n            if (!existing || Number(schedule.id) > Number(existing.id)) {\n                map.set(key, schedule);\n            }\n        }\n        return Array.from(map.values());\n    };\n    // Create combined trip data that merges tripReport and tripReportSchedules\n    const combinedTripData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const existingTrips = (tripReport || []).filter((trip)=>!(trip === null || trip === void 0 ? void 0 : trip.archived)).map((trip)=>{\n            var _trip_fromLocation, _trip_fromLocation1, _trip_toLocation, _trip_toLocation1, _trip_fromLocation2, _trip_toLocation2;\n            return {\n                ...trip,\n                isScheduled: false,\n                isCreated: true,\n                sortTime: trip.departTime || \"00:00:00\",\n                displayText: \"<div>\".concat((trip === null || trip === void 0 ? void 0 : trip.departTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.departTime) + \" - \" : \"No depart time - \").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation = trip.fromLocation) === null || _trip_fromLocation === void 0 ? void 0 : _trip_fromLocation.title) || \"\").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation1 = trip.fromLocation) === null || _trip_fromLocation1 === void 0 ? void 0 : _trip_fromLocation1.title) && (trip === null || trip === void 0 ? void 0 : (_trip_toLocation = trip.toLocation) === null || _trip_toLocation === void 0 ? void 0 : _trip_toLocation.title) ? \" -> \" : \"\", '<span class=\"').concat((0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_38__.isVesselArrivalLate)(trip === null || trip === void 0 ? void 0 : trip.arriveTime, trip === null || trip === void 0 ? void 0 : trip.arrive) ? \"text-cinnabar-500\" : \"\", '\">').concat((trip === null || trip === void 0 ? void 0 : trip.arrive) ? convertTimeFormat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(trip === null || trip === void 0 ? void 0 : trip.arrive).format(\"HH:mm \") + \" - \") : (trip === null || trip === void 0 ? void 0 : trip.arriveTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.arriveTime) + \" - \" : \" No arrival time \", \"</span> \").concat((trip === null || trip === void 0 ? void 0 : (_trip_toLocation1 = trip.toLocation) === null || _trip_toLocation1 === void 0 ? void 0 : _trip_toLocation1.title) || \"\").concat(!(trip === null || trip === void 0 ? void 0 : (_trip_fromLocation2 = trip.fromLocation) === null || _trip_fromLocation2 === void 0 ? void 0 : _trip_fromLocation2.title) && !(trip === null || trip === void 0 ? void 0 : (_trip_toLocation2 = trip.toLocation) === null || _trip_toLocation2 === void 0 ? void 0 : _trip_toLocation2.title) ? \" \" : \" \", \"</div>\")\n            };\n        });\n        const latestUniqueSchedules = getLatestUniqueSchedules(tripReportSchedules);\n        // Get current time for filtering\n        const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n        const scheduledTrips = (latestUniqueSchedules || []).map((schedule)=>{\n            // Check if this schedule has already been created as a trip\n            const isAlreadyCreated = existingTrips.some((trip)=>trip.tripReportScheduleID === schedule.id);\n            return {\n                ...schedule,\n                isScheduled: true,\n                isCreated: isAlreadyCreated,\n                sortTime: schedule.departTime || \"00:00:00\",\n                displayText: \"\".concat(schedule.departTime, \" - \").concat(schedule.arriveTime, \" | \").concat(schedule.fromLocation.title, \" → \").concat(schedule.toLocation.title)\n            };\n        });\n        // Filter scheduled trips based on time and showPastScheduledTrips state\n        const filteredScheduledTrips = scheduledTrips.filter((s)=>{\n            if (s.isCreated) return false // Don't show already created trips\n            ;\n            const tripTime = s.sortTime || \"00:00:00\";\n            const isUpcoming = tripTime >= currentTime;\n            // Show upcoming trips by default, past trips only when showPastScheduledTrips is true\n            return showPastScheduledTrips ? !isUpcoming : isUpcoming;\n        });\n        // Combine and sort by departure time\n        const combined = [\n            ...existingTrips,\n            ...filteredScheduledTrips\n        ];\n        return combined.sort((a, b)=>{\n            const timeA = a.sortTime || \"00:00:00\";\n            const timeB = b.sortTime || \"00:00:00\";\n            return timeA.localeCompare(timeB);\n        });\n    }, [\n        tripReport,\n        tripReportSchedules,\n        showPastScheduledTrips\n    ]);\n    // Check if there are past scheduled trips available\n    const hasPastScheduledTrips = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n        const latestUniqueSchedules = getLatestUniqueSchedules(tripReportSchedules);\n        const existingTrips = tripReport || [];\n        return latestUniqueSchedules.some((schedule)=>{\n            const isAlreadyCreated = existingTrips.some((trip)=>trip.tripReportScheduleID === schedule.id);\n            const tripTime = schedule.departTime || \"00:00:00\";\n            const isPast = tripTime < currentTime;\n            return !isAlreadyCreated && isPast;\n        });\n    }, [\n        tripReport,\n        tripReportSchedules\n    ]);\n    // Handle creating a trip from a scheduled item\n    const handleCreateFromSchedule = (scheduleItem)=>{\n        setSelectedTripReportSchedule(scheduleItem);\n        const input = {\n            tripReportScheduleID: scheduleItem.id,\n            departTime: scheduleItem.departTime,\n            arriveTime: scheduleItem.arriveTime,\n            fromLocationID: scheduleItem.fromLocationID,\n            toLocationID: scheduleItem.toLocationID,\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, true) // true indicates this is a scheduled trip\n        ;\n    };\n    // Implement confirmDeletetrip using  AlertNew component to confirm trip deletion\n    const confirmDeleteTrip = (item)=>{\n        if (item) {\n            setDeleteTripItem(item);\n            setDeleteTripConfirmation(true);\n        }\n    };\n    const [deleteTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.DeleteTripReport_LogBookEntrySections, {\n        onCompleted: ()=>{\n            // const data = response.deleteTripReport_LogBookEntrySection\n            // updateTripReport({\n            //     id: data,\n            //     key: 'archived',\n            //     value: true,\n            // })\n            updateTripReport({\n                id: [\n                    ...tripReport.filter((trip)=>trip.id !== deleteTripItem.id).map((trip)=>trip.id)\n                ]\n            });\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting trip report\", error);\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    });\n    const handleDeleteTrip = async ()=>{\n        if (deleteTripItem) {\n            if (!offline) {\n                await deleteTripReport_LogBookEntrySection({\n                    variables: {\n                        ids: [\n                            deleteTripItem.id\n                        ]\n                    }\n                });\n            } else {\n                await tripReportModel.save({\n                    id: deleteTripItem.id,\n                    archived: true\n                });\n            }\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: allowedVesselTypes.includes(vessel.vesselType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex xs:flex-row flex-col xs:justify-between xs:gap-2 xs:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_20__.Combobox, {\n                            options: tripScheduleServices,\n                            value: tabTSS ? tripScheduleServices.find((option)=>option.value === tabTSS) || null : tripScheduleServices.find((option)=>option.value === selectedTripScheduleServiceID) || null,\n                            onChange: (e)=>{\n                                setTabTSS((e === null || e === void 0 ? void 0 : e.value) || \"\");\n                                if (e) {\n                                    setSelectedTripScheduleServiceID(e.value);\n                                    loadTripReportSchedules(e.value);\n                                } else {\n                                    setSelectedTripScheduleServiceID(null);\n                                    setTripReportSchedules([]);\n                                    setShowNextTrips(false);\n                                }\n                            },\n                            placeholder: \"Select Trip Schedule Service\",\n                            disabled: locked\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1264,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                            onClick: handleCustomTrip,\n                            variant: \"primary\",\n                            disabled: locked,\n                            children: \"Add Non-Scheduled Trip\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1292,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1263,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1261,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: \"This section covers the logbook entry. This can be made up of a single trip or many over the course of the voyage.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1324,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: combinedTripData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        hasPastScheduledTrips && !showPastScheduledTrips && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                onClick: ()=>setShowPastScheduledTrips(true),\n                                variant: \"primary\",\n                                children: \"Show Past Trips\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 1335,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1334,\n                            columnNumber: 29\n                        }, this),\n                        showPastScheduledTrips && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                onClick: ()=>setShowPastScheduledTrips(false),\n                                variant: \"primary\",\n                                children: \"Hide Past Trips\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 1348,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1347,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.Accordion, {\n                            type: \"single\",\n                            collapsible: true,\n                            value: accordionValue,\n                            onValueChange: (value)=>{\n                                setAccordionValue(value);\n                                // If we're closing the accordion, reset the state\n                                if (value === \"\") {\n                                    setSelectedTab(0);\n                                    setOpenTripModal(false);\n                                    setCurrentTrip(false);\n                                    setSelectedTripReportSchedule(null);\n                                } else {\n                                    // Find the selected item from combined data\n                                    const selectedItem = combinedTripData.find((item)=>item.id.toString() === value);\n                                    if (selectedItem) {\n                                        if (selectedItem.isScheduled && !selectedItem.isCreated) {\n                                            // This is a scheduled trip that hasn't been created yet\n                                            // Don't set currentTrip, just expand the accordion\n                                            setSelectedTab(0);\n                                            setOpenTripModal(false);\n                                            setCurrentTrip(false);\n                                            setSelectedTripReportSchedule(null);\n                                        } else {\n                                            // This is an existing trip or a created scheduled trip\n                                            setSelectedTab(selectedItem.id);\n                                            setOpenTripModal(true);\n                                            setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default()());\n                                            currentTripRef.current = selectedItem;\n                                            setCurrentTrip(selectedItem);\n                                            setSelectedTripReportSchedule(null);\n                                            // Initialize signature data if available\n                                            if (selectedItem.sectionSignature) {\n                                                setSignature(selectedItem.sectionSignature.signatureData || \"\");\n                                            } else {\n                                                setSignature(\"\");\n                                            }\n                                            setRiskBufferEvDgr(selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.dangerousGoodsChecklist);\n                                            setOpenTripStartRiskAnalysis(false);\n                                            setAllDangerousGoods(false);\n                                            setCurrentStopEvent(false);\n                                            setCurrentEventTypeEvent(false);\n                                            setSelectedRowEvent(false);\n                                            setDisplayDangerousGoods((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.enableDGR) === true);\n                                            setDisplayDangerousGoodsSailing((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.designatedDangerousGoodsSailing) === true);\n                                            setDisplayDangerousGoodsPvpd(false);\n                                            setDisplayDangerousGoodsPvpdSailing(null);\n                                            setAllPVPDDangerousGoods(false);\n                                            setSelectedDGRPVPD(false);\n                                            setTripReport_Stops(false);\n                                        }\n                                    }\n                                }\n                            },\n                            children: combinedTripData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionItem, {\n                                    value: item.id.toString(),\n                                    id: \"combined-trip-\".concat(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-2 min-h-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row sm:items-center xs:gap-x-2 flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm sm:text-base font-medium truncate\",\n                                                                dangerouslySetInnerHTML: {\n                                                                    __html: item.displayText\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1444,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            item.isScheduled && !item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded whitespace-nowrap self-start sm:self-auto\",\n                                                                children: \"Scheduled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1452,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            !item.isScheduled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap xs:gap-2 text-xs sm:text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"whitespace-nowrap\",\n                                                                        children: [\n                                                                            \"POB: \",\n                                                                            item.totalPOB\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1458,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"whitespace-nowrap\",\n                                                                        children: [\n                                                                            \"Total Pax Carried:\",\n                                                                            \" \",\n                                                                            item.totalPaxJoined\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1461,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"whitespace-nowrap\",\n                                                                        children: [\n                                                                            \"Total Vehicles Carried:\",\n                                                                            \" \",\n                                                                            item.totalVehiclesJoined\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1467,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1457,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1443,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 self-end sm:self-auto\",\n                                                        children: [\n                                                            !item.isScheduled && item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleCreateFromSchedule(item);\n                                                                },\n                                                                size: \"sm\",\n                                                                disabled: locked,\n                                                                children: \"Create Trip\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1480,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            !item.isScheduled && !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        confirmDeleteTrip(item);\n                                                                    },\n                                                                    iconLeft: _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n                                                                    size: \"sm\",\n                                                                    variant: \"destructive\",\n                                                                    disabled: locked,\n                                                                    children: \"Delete Trip\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                    lineNumber: 1495,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            }, void 0, false)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1477,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1442,\n                                                columnNumber: 41\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1441,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionContent, {\n                                            className: \"px-5 sm:px-10\",\n                                            children: !item.isScheduled || item.isCreated ? currentTrip && currentTrip.id === item.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TripReportAccordionContent, {\n                                                currentTrip: currentTrip,\n                                                offline: offline,\n                                                tripReport: tripReport,\n                                                updateTripReport: updateTripReport,\n                                                updateTripReport_LogBookEntrySection: updateTripReport_LogBookEntrySection,\n                                                currentTripRef: currentTripRef,\n                                                locations: locations,\n                                                locked: locked,\n                                                edit_tripReport: edit_tripReport,\n                                                vessel: vessel,\n                                                crewMembers: crewMembers,\n                                                logBookConfig: logBookConfig,\n                                                client: client,\n                                                canCarryVehicles: canCarryVehicles,\n                                                canCarryDangerousGoods: canCarryDangerousGoods,\n                                                selectedDGR: selectedDGR,\n                                                displayDangerousGoods: displayDangerousGoods,\n                                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                                allDangerousGoods: allDangerousGoods,\n                                                setAllDangerousGoods: setAllDangerousGoods,\n                                                logBookStartDate: logBookStartDate,\n                                                masterID: masterID,\n                                                vessels: vessels,\n                                                setSelectedRowEvent: setSelectedRowEvent,\n                                                setCurrentEventTypeEvent: setCurrentEventTypeEvent,\n                                                setCurrentStopEvent: setCurrentStopEvent,\n                                                currentEventTypeEvent: currentEventTypeEvent,\n                                                currentStopEvent: currentStopEvent,\n                                                tripReport_Stops: tripReport_Stops,\n                                                setTripReport_Stops: setTripReport_Stops,\n                                                displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                                                setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                                                displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                                                setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                selectedDGRPVPD: selectedDGRPVPD,\n                                                setSelectedDGRPVPD: setSelectedDGRPVPD,\n                                                fuelLogs: fuelLogs,\n                                                comment: comment,\n                                                setComment: setComment,\n                                                displayFieldTripLog: displayFieldTripLog,\n                                                signatureKey: signatureKey,\n                                                signature: signature,\n                                                setSignature: setSignature,\n                                                handleCancel: handleCancel,\n                                                handleSave: handleSave\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1544,\n                                                columnNumber: 49\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: 'Click the \"Create Trip\" button above to create this scheduled trip and access the trip details.'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                    lineNumber: 1658,\n                                                    columnNumber: 49\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1657,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1539,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, \"combined-trip-\".concat(item.id, \"-\").concat(item.isScheduled ? \"scheduled\" : \"existing\"), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1437,\n                                    columnNumber: 33\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1358,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1329,\n                columnNumber: 13\n            }, this),\n            !allowedVesselTypes.includes(vessel.vesselType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                    onClick: handleCustomTrip,\n                    variant: \"primary\",\n                    disabled: locked,\n                    children: \"Add Trip\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1676,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1675,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__.AlertDialogNew, {\n                openDialog: deleteTripConfirmation,\n                setOpenDialog: setDeleteTripConfirmation,\n                // handleCreate={handleDeleteTrip}\n                title: \"Delete Trip\",\n                description: \"Are you sure you want to delete this trip? This action cannot be undone.\",\n                cancelText: \"Cancel\",\n                destructiveActionText: \"Delete\",\n                handleDestructiveAction: handleDeleteTrip,\n                showDestructiveAction: true,\n                variant: \"danger\",\n                actionText: \"Delete\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1686,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TripLog, \"FR7Uy5QoV0u7vKFnRK0BDQ6bE08=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        nuqs__WEBPACK_IMPORTED_MODULE_40__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_40__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation\n    ];\n});\n_c1 = TripLog;\nvar _c, _c1;\n$RefreshReg$(_c, \"TripReportAccordionContent\");\n$RefreshReg$(_c1, \"TripLog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx\n"));

/***/ })

});