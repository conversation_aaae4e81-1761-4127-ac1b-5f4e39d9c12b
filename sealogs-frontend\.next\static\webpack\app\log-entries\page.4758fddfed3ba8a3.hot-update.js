"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/logbook/trip-log.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripLog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/eventType */ \"(app-pages-browser)/./src/app/offline/models/eventType.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _depart_time__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./depart-time */ \"(app-pages-browser)/./src/app/ui/logbook/depart-time.tsx\");\n/* harmony import */ var _exp_arrival_time__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./exp-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/exp-arrival-time.tsx\");\n/* harmony import */ var _actual_arrival_time__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./actual-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./events */ \"(app-pages-browser)/./src/app/ui/logbook/events.tsx\");\n/* harmony import */ var _trip_log_pob__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./trip-log-pob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx\");\n/* harmony import */ var _trip_log_vob__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./trip-log-vob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-vob.tsx\");\n/* harmony import */ var _trip_log_dgr__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./trip-log-dgr */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-dgr.tsx\");\n/* harmony import */ var _components_trip_comments__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./components/trip-comments */ \"(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_master__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./components/master */ \"(app-pages-browser)/./src/app/ui/logbook/components/master.tsx\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React and Next.js imports\n\n\n\n\n\n\n// Utility imports\n\n\n\n\n\n\n\n// Model imports\n\n\n\n\n\n// UI Component imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Reusable TripReportAccordionContent component\nconst TripReportAccordionContent = (param)=>{\n    let { currentTrip, offline, tripReport, updateTripReport, updateTripReport_LogBookEntrySection, currentTripRef, locations, locked, edit_tripReport, vessel, crewMembers, logBookConfig, client, canCarryVehicles, canCarryDangerousGoods, selectedDGR, displayDangerousGoods, setDisplayDangerousGoods, displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing, allDangerousGoods, setAllDangerousGoods, logBookStartDate, masterID, vessels, setSelectedRowEvent, setCurrentEventTypeEvent, setCurrentStopEvent, currentEventTypeEvent, currentStopEvent, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, comment, setComment, displayFieldTripLog, signatureKey, signature, setSignature, handleCancel, handleSave } = param;\n    var _currentTripRef_current;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"space-y-6\", locked || !edit_tripReport ? \"opacity-70 pointer-events-none\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col inset-y-0 items-center pt-10 pb-4 absolute -left-5 sm:-left-[25px] min-h-24 w-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"border-l h-full border-wedgewood-200 border-dashed\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_depart_time__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        templateStyle: \"\",\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                        label: \"Departure location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLocation)=>{\n                                // Update the from location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"fromLocationID\",\n                                        value: selectedLocation.value,\n                                        label: selectedLocation.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                fromLocationID: selectedLocation.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_18__.H5, {\n                        children: \"PEOPLE ON BOARD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_pob__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        vessel: vessel,\n                        crewMembers: crewMembers,\n                        logBookConfig: logBookConfig,\n                        masterTerm: client === null || client === void 0 ? void 0 : client.masterTerm,\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            canCarryVehicles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_vob__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                offline: offline,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 25\n                            }, undefined),\n                            canCarryDangerousGoods && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_dgr__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                offline: offline,\n                                locked: locked || !edit_tripReport,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig,\n                                selectedDGR: selectedDGR,\n                                members: crewMembers,\n                                displayDangerousGoods: displayDangerousGoods,\n                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                allDangerousGoods: allDangerousGoods,\n                                setAllDangerousGoods: setAllDangerousGoods\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_events__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        offline: offline,\n                        logBookStartDate: logBookStartDate,\n                        currentTrip: currentTrip,\n                        logBookConfig: logBookConfig,\n                        updateTripReport: updateTripReport,\n                        locked: locked,\n                        geoLocations: locations,\n                        tripReport: tripReport,\n                        crewMembers: crewMembers,\n                        masterID: masterID,\n                        vessel: vessel,\n                        vessels: vessels,\n                        setSelectedRow: setSelectedRowEvent,\n                        setCurrentEventType: setCurrentEventTypeEvent,\n                        setCurrentStop: setCurrentStopEvent,\n                        currentEventType: currentEventTypeEvent,\n                        currentStop: currentStopEvent,\n                        tripReport_Stops: tripReport_Stops,\n                        setTripReport_Stops: setTripReport_Stops,\n                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                        selectedDGRPVPD: selectedDGRPVPD,\n                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                        fuelLogs: fuelLogs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                        label: \"Arrival location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLoc)=>{\n                                // Update the to location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"toLocationID\",\n                                        value: selectedLoc.value,\n                                        label: selectedLoc.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                toLocationID: selectedLoc.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 133,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_exp_arrival_time__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 342,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_actual_arrival_time__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 349,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trip_comments__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                setCommentField: setComment,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 356,\n                columnNumber: 13\n            }, undefined),\n            displayFieldTripLog(\"MasterID\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked || !edit_tripReport ? \"pointer-events-none\" : \"\", \" max-w-sm\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_master__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                    offline: offline,\n                    currentTrip: currentTrip,\n                    tripReport: tripReport,\n                    crewMembers: crewMembers,\n                    updateTripReport: updateTripReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 364,\n                columnNumber: 17\n            }, undefined),\n            displayFieldTripLog(\"Signature\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                locked: locked,\n                title: \"Signature Confirmation\",\n                description: \"By signing below, I confirm that the recorded entries are accurate to the best of my knowledge and in accordance with the vessel's operating procedures and regulations.\",\n                signature: currentTripRef === null || currentTripRef === void 0 ? void 0 : (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.sectionSignature,\n                onSignatureChanged: (sign)=>{\n                    setSignature(sign);\n                }\n            }, \"\".concat(signatureKey, \"-\").concat(currentTrip.id), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 377,\n                columnNumber: 17\n            }, undefined),\n            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_36__.FormFooter, {\n                className: \"justify-end flex-nowrap xs:gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        variant: \"back\",\n                        onClick: handleCancel,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        iconLeft: _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"],\n                        onClick: handleSave,\n                        children: \"Update\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 390,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n        lineNumber: 126,\n        columnNumber: 9\n    }, undefined);\n};\n_c = TripReportAccordionContent;\nfunction TripLog(param) {\n    let { tripReport = false, logBookConfig, updateTripReport, locked, crewMembers, masterID, createdTab = false, setCreatedTab, currentTrip = false, setCurrentTrip, vessels, offline = false, fuelLogs, logBookStartDate } = param;\n    var _tripReport_find;\n    _s();\n    // const router = useRouter()\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const allowedVesselTypes = [\n        \"SLALL\",\n        \"Tug_Boat\",\n        \"Passenger_Ferry\",\n        \"Water_Taxi\"\n    ];\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_42__.useQueryState)(\"tab\", {\n        defaultValue: \"crew\"\n    });\n    const [tabTSS, setTabTSS] = (0,nuqs__WEBPACK_IMPORTED_MODULE_42__.useQueryState)(\"tss\", {\n        defaultValue: \"\"\n    });\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eventTypes, setEventTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openTripModal, setOpenTripModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bufferTripID, setBufferTripID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDGR, setSelectedDGR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoods, setDisplayDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [openEventModal, setOpenEventModal] = useState(false)\n    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)\n    const [tripReport_Stops, setTripReport_Stops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDGRPVPD, setSelectedDGRPVPD] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRowEvent, setSelectedRowEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [riskBufferEvDgr, setRiskBufferEvDgr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDangerousGoods, setAllDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEventTypeEvent, setCurrentEventTypeEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStopEvent, setCurrentStopEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signatureKey, setSignatureKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default()());\n    const currentTripRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [deleteTripItem, setDeleteTripItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteTripConfirmation, setDeleteTripConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canCarryDangerousGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesDangerousGoods);\n    }, [\n        vessel\n    ]);\n    const canCarryVehicles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesVehicles);\n    }, [\n        vessel\n    ]);\n    // Initialize client\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_10__.getOneClient)(setClient);\n    }\n    // Update signature state when currentTrip changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentTrip && currentTrip.sectionSignature) {\n            setSignature(currentTrip.sectionSignature.signatureData || \"\");\n        } else {\n            setSignature(\"\");\n        }\n    }, [\n        currentTrip\n    ]);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(tripReport ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.comment : \"\");\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_tripReport, setEdit_tripReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const clientModel = new _app_offline_models_client__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const eventTypeModel = new _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_16__[\"default\"]();\n    const [openTripSelectionDialog, setOpenTripSelectionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tripReportSchedules, setTripReportSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripReportSchedule, setSelectedTripReportSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tripScheduleServices, setTripScheduleServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNextTrips, setShowNextTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreatingScheduledTrip, setIsCreatingScheduledTrip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPastScheduledTrips, setShowPastScheduledTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Responsive hooks\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_40__.useBreakpoints)();\n    const responsiveLabel = (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_39__.useResponsiveLabel)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"EDIT_LOGBOOKENTRY_TRIPREPORT\", permissions)) {\n                setEdit_tripReport(true);\n            } else {\n                setEdit_tripReport(false);\n            }\n        }\n    };\n    const offlineLoad = async ()=>{\n        const locations = await geoLocationModel.getAll();\n        setLocations(locations);\n        const types = await eventTypeModel.getAll();\n        setEventTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.getPermissions);\n        loadTripScheduleServices();\n        if (!locations) {\n            if (offline) {\n                offlineLoad();\n            } else {\n                loadLocations();\n                loadEventTypes();\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (createdTab) {\n            setSelectedTab(createdTab);\n        }\n    }, [\n        createdTab\n    ]);\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_10__.getVesselByID)(+vesselID, setVessel);\n    }\n    const scrollToAccordionItem = (tripId)=>{\n        const element = document.getElementById(\"triplog-\".concat(tripId));\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport && currentTrip) {\n            const trip = tripReport.find((trip)=>trip.id === currentTrip.id);\n            currentTripRef.current = trip;\n            setCurrentTrip(trip);\n        }\n        if (tripReport && bufferTripID > 0) {\n            const trip = tripReport.find((trip)=>trip.id === bufferTripID);\n            if (trip) {\n                currentTripRef.current = trip;\n                setCurrentTrip(trip);\n                // Only expand accordion and scroll for regular trips, not scheduled trips\n                if (!selectedTripReportSchedule) {\n                    setAccordionValue(trip.id.toString());\n                    scrollToAccordionItem(trip.id);\n                }\n                setOpenTripModal(true);\n                setSelectedTab(trip.id);\n                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default()());\n                // Initialize signature data if available\n                if (trip.sectionSignature) {\n                    setSignature(trip.sectionSignature.signatureData || \"\");\n                } else {\n                    setSignature(\"\");\n                }\n                // Initialize trip-specific state\n                setRiskBufferEvDgr(trip === null || trip === void 0 ? void 0 : trip.dangerousGoodsChecklist);\n                setOpenTripStartRiskAnalysis(false);\n                setAllDangerousGoods(false);\n                setCurrentStopEvent(false);\n                setCurrentEventTypeEvent(false);\n                setSelectedRowEvent(false);\n                setDisplayDangerousGoods((trip === null || trip === void 0 ? void 0 : trip.enableDGR) === true);\n                setDisplayDangerousGoodsSailing((trip === null || trip === void 0 ? void 0 : trip.designatedDangerousGoodsSailing) === true);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setAllPVPDDangerousGoods(false);\n                setSelectedDGRPVPD(false);\n                setTripReport_Stops(false);\n            }\n            setBufferTripID(0);\n        }\n    }, [\n        tripReport\n    ]);\n    const [loadLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setLocations(response.readGeoLocations.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading locations\", error);\n        }\n    });\n    const [loadEventTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_EVENT_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setEventTypes(response.readEventTypes.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading activity types\", error);\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_Stop, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const handleCreateTripReportScheduleStops = async (logBookEntrySectionID)=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(selectedTripReportSchedule)) {\n            const tripStops = selectedTripReportSchedule.tripReportScheduleStops.nodes || [];\n            await Promise.all(tripStops.map(async (stop)=>{\n                const input = {\n                    logBookEntrySectionID: logBookEntrySectionID,\n                    tripReportScheduleStopID: stop.id,\n                    arriveTime: stop.arriveTime,\n                    departTime: stop.departTime,\n                    stopLocationID: stop.stopLocationID\n                };\n                await createTripReport_Stop({\n                    variables: {\n                        input: input\n                    }\n                });\n            }));\n            setSelectedTripReportSchedule(null);\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        logBookEntrySectionID\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        logBookEntrySectionID\n                    ]\n                });\n            }\n        }\n    };\n    const [createTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            handleCreateTripReportScheduleStops(data.id);\n            // Always set accordion value and scroll for newly created trips\n            setAccordionValue(data.id.toString());\n            scrollToAccordionItem(data.id);\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n            setCurrentTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [readTripReportSchedules, { loading: readTripReportSchedulesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripReportSchedules, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripReportSchedules.nodes.filter((trip)=>// only show trips for the current vessel\n                trip.vehicles.nodes.some((vehicle)=>+vehicle.id === +vesselID));\n            if (showNextTrips) {\n                // only show 1 past trip and 4 upcoming trips\n                const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n                const pastIndex = data.findIndex((trip)=>trip.departTime >= currentTime);\n                const result = (pastIndex > 0 ? [\n                    data[pastIndex - 1]\n                ] : []).concat(data.slice(pastIndex, pastIndex + 4));\n                setTripReportSchedules(result);\n            } else {\n                setTripReportSchedules(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripReportSchedules\", error);\n        }\n    });\n    const [readTripScheduleServices] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripScheduleServices, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripScheduleServices.nodes.map((tss)=>{\n                return {\n                    label: tss.title,\n                    value: tss.id\n                };\n            });\n            setTripScheduleServices(data);\n            setTripReportSchedules([]);\n        // setOpenTripSelectionDialog(true)\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripScheduleServices\", error);\n        }\n    });\n    const loadTripScheduleServices = async ()=>{\n        await readTripScheduleServices({\n            variables: {\n                filter: {\n                    vehicles: {\n                        id: {\n                            eq: vesselID\n                        }\n                    }\n                }\n            }\n        });\n    };\n    const loadTripReportSchedules = async (tripScheduleServiceID)=>{\n        setTripReportSchedules([]);\n        await readTripReportSchedules({\n            variables: {\n                filter: {\n                    // archived: { eq: false },\n                    // start: { eq: logBookStartDate },\n                    tripScheduleServiceID: {\n                        eq: tripScheduleServiceID\n                    }\n                }\n            }\n        });\n    };\n    const doCreateTripReport = async function(input) {\n        let isScheduledTrip = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!edit_tripReport) {\n            sonner__WEBPACK_IMPORTED_MODULE_17__.toast.error(\"You do not have permission to add a trip\");\n            return;\n        }\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setIsCreatingScheduledTrip(isScheduledTrip);\n        if (offline) {\n            const data = await tripReportModel.save({\n                ...input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_11__.generateUniqueId)()\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            // For offline mode, immediately set accordion value since the trip is created synchronously\n            setAccordionValue(data.id.toString());\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        } else {\n            createTripReport_LogBookEntrySection({\n                variables: {\n                    input: input\n                }\n            });\n        }\n        setRiskBufferEvDgr(false);\n        setOpenTripStartRiskAnalysis(false);\n        setAllDangerousGoods(false);\n        setCurrentStopEvent(false);\n        setCurrentEventTypeEvent(false);\n        setSelectedRowEvent(false);\n        setDisplayDangerousGoods(false);\n        setDisplayDangerousGoodsSailing(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        setAllPVPDDangerousGoods(false);\n        setSelectedDGRPVPD(false);\n        setTripReport_Stops(false);\n    };\n    const handleCustomTrip = ()=>{\n        setOpenTripSelectionDialog(false);\n        const input = {\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, false) // false indicates this is a regular trip\n        ;\n    };\n    // Removed unused handleEditTrip function\n    const [createLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: +data.logBookEntrySectionID,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error saving signature\", error);\n        }\n    });\n    const [updateLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            var _currentTripRef_current;\n            const data = response.updateLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"341 TripLog updateLogBookEntrySection_Signature\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        // Log signature information for debugging\n        // Ensure we have a valid signature\n        const sigVariables = {\n            logBookEntrySectionID: currentTrip.id,\n            memberID: localStorage.getItem(\"userId\"),\n            signatureData: signature || \"\"\n        };\n        if (+currentTrip.sectionSignatureID > 0) {\n            // Update signature\n            updateLogBookEntrySection_Signature({\n                variables: {\n                    input: {\n                        ...sigVariables,\n                        id: +currentTrip.sectionSignatureID\n                    }\n                }\n            });\n        } else {\n            // Create signature\n            createLogBookEntrySection_Signature({\n                variables: {\n                    input: sigVariables !== null && sigVariables !== void 0 ? sigVariables : \"\"\n                }\n            });\n        }\n        if (offline) {\n            // updateTripReport_LogBookEntrySection\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                comment: comment || null\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n        } else {\n            var _currentTripRef_current;\n            await updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        comment: comment || null\n                    }\n                }\n            });\n            setOpenTripModal(false);\n            setCurrentTrip(false);\n        }\n    };\n    const displayFieldTripLog = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"TripReport_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const convertTimeFormat = (time)=>{\n        if (time === null || time === undefined) return \"\";\n        const [hours, minutes] = time.split(\":\");\n        return \"\".concat(hours, \":\").concat(minutes);\n    };\n    const handleCancel = ()=>{\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setAccordionValue(\"\");\n    };\n    // Removed unused functions\n    const initOffline = async ()=>{\n        var _localStorage_getItem;\n        // getOneClient\n        const client = await clientModel.getById((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n        setClient(client);\n        // getVesselByID(+vesselID, setVessel)\n        const vessel = await vesselModel.getById(vesselID);\n        setVessel(vessel);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            initOffline();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_7___default()(tabTSS))) {\n            loadTripReportSchedules(tabTSS);\n        }\n    }, [\n        tabTSS\n    ]);\n    /**\r\n     * Takes an array of trip report schedules and returns an array of the latest unique schedules based on:\r\n     * - departTime\r\n     * - arriveTime\r\n     * - fromLocationID\r\n     * - toLocationID\r\n     * - start\r\n     *\r\n     * Schedules with the same combination of the above properties will be deduplicated, with the latest schedule (based on id) being kept.\r\n     *\r\n     * @param {TripReportSchedule[]} schedules - An array of trip report schedules\r\n     * @returns {TripReportSchedule[]} - An array of the latest unique trip report schedules\r\n     */ const getLatestUniqueSchedules = (schedules)=>{\n        const map = new Map();\n        for (const schedule of schedules){\n            const key = \"\".concat(schedule.departTime, \"|\").concat(schedule.arriveTime, \"|\").concat(schedule.fromLocationID, \"|\").concat(schedule.toLocationID, \"|\").concat(schedule.start);\n            const existing = map.get(key);\n            if (!existing || Number(schedule.id) > Number(existing.id)) {\n                map.set(key, schedule);\n            }\n        }\n        return Array.from(map.values());\n    };\n    // Create combined trip data that merges tripReport and tripReportSchedules\n    const combinedTripData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const existingTrips = (tripReport || []).filter((trip)=>!(trip === null || trip === void 0 ? void 0 : trip.archived)).map((trip)=>{\n            var _trip_fromLocation, _trip_fromLocation1, _trip_toLocation, _trip_toLocation1, _trip_fromLocation2, _trip_toLocation2;\n            return {\n                ...trip,\n                isScheduled: false,\n                isCreated: true,\n                sortTime: trip.departTime || \"00:00:00\",\n                displayText: \"<div>\".concat((trip === null || trip === void 0 ? void 0 : trip.departTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.departTime) + \" - \" : \"No depart time - \").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation = trip.fromLocation) === null || _trip_fromLocation === void 0 ? void 0 : _trip_fromLocation.title) || \"\").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation1 = trip.fromLocation) === null || _trip_fromLocation1 === void 0 ? void 0 : _trip_fromLocation1.title) && (trip === null || trip === void 0 ? void 0 : (_trip_toLocation = trip.toLocation) === null || _trip_toLocation === void 0 ? void 0 : _trip_toLocation.title) ? \" -> \" : \"\", '<span class=\"').concat((0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_38__.isVesselArrivalLate)(trip === null || trip === void 0 ? void 0 : trip.arriveTime, trip === null || trip === void 0 ? void 0 : trip.arrive) ? \"text-cinnabar-500\" : \"\", '\">').concat((trip === null || trip === void 0 ? void 0 : trip.arrive) ? convertTimeFormat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(trip === null || trip === void 0 ? void 0 : trip.arrive).format(\"HH:mm \") + \" - \") : (trip === null || trip === void 0 ? void 0 : trip.arriveTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.arriveTime) + \" - \" : \" No arrival time \", \"</span> \").concat((trip === null || trip === void 0 ? void 0 : (_trip_toLocation1 = trip.toLocation) === null || _trip_toLocation1 === void 0 ? void 0 : _trip_toLocation1.title) || \"\").concat(!(trip === null || trip === void 0 ? void 0 : (_trip_fromLocation2 = trip.fromLocation) === null || _trip_fromLocation2 === void 0 ? void 0 : _trip_fromLocation2.title) && !(trip === null || trip === void 0 ? void 0 : (_trip_toLocation2 = trip.toLocation) === null || _trip_toLocation2 === void 0 ? void 0 : _trip_toLocation2.title) ? \" \" : \" \", \"</div>\")\n            };\n        });\n        const latestUniqueSchedules = getLatestUniqueSchedules(tripReportSchedules);\n        // Get current time for filtering\n        const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n        const scheduledTrips = (latestUniqueSchedules || []).map((schedule)=>{\n            // Check if this schedule has already been created as a trip\n            const isAlreadyCreated = existingTrips.some((trip)=>trip.tripReportScheduleID === schedule.id);\n            return {\n                ...schedule,\n                isScheduled: true,\n                isCreated: isAlreadyCreated,\n                sortTime: schedule.departTime || \"00:00:00\",\n                displayText: \"\".concat(schedule.departTime, \" - \").concat(schedule.arriveTime, \" | \").concat(schedule.fromLocation.title, \" → \").concat(schedule.toLocation.title)\n            };\n        });\n        // Filter scheduled trips based on time and showPastScheduledTrips state\n        const filteredScheduledTrips = scheduledTrips.filter((s)=>{\n            if (s.isCreated) return false // Don't show already created trips\n            ;\n            const tripTime = s.sortTime || \"00:00:00\";\n            const isUpcoming = tripTime >= currentTime;\n            // Show upcoming trips by default, past trips only when showPastScheduledTrips is true\n            return showPastScheduledTrips ? !isUpcoming : isUpcoming;\n        });\n        // Combine and sort by departure time\n        const combined = [\n            ...existingTrips,\n            ...filteredScheduledTrips\n        ];\n        return combined.sort((a, b)=>{\n            const timeA = a.sortTime || \"00:00:00\";\n            const timeB = b.sortTime || \"00:00:00\";\n            return timeA.localeCompare(timeB);\n        });\n    }, [\n        tripReport,\n        tripReportSchedules,\n        showPastScheduledTrips\n    ]);\n    // Check if there are past scheduled trips available\n    const hasPastScheduledTrips = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n        const latestUniqueSchedules = getLatestUniqueSchedules(tripReportSchedules);\n        const existingTrips = tripReport || [];\n        return latestUniqueSchedules.some((schedule)=>{\n            const isAlreadyCreated = existingTrips.some((trip)=>trip.tripReportScheduleID === schedule.id);\n            const tripTime = schedule.departTime || \"00:00:00\";\n            const isPast = tripTime < currentTime;\n            return !isAlreadyCreated && isPast;\n        });\n    }, [\n        tripReport,\n        tripReportSchedules\n    ]);\n    // Handle creating a trip from a scheduled item\n    const handleCreateFromSchedule = (scheduleItem)=>{\n        setSelectedTripReportSchedule(scheduleItem);\n        const input = {\n            tripReportScheduleID: scheduleItem.id,\n            departTime: scheduleItem.departTime,\n            arriveTime: scheduleItem.arriveTime,\n            fromLocationID: scheduleItem.fromLocationID,\n            toLocationID: scheduleItem.toLocationID,\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, true) // true indicates this is a scheduled trip\n        ;\n    };\n    // Implement confirmDeletetrip using  AlertNew component to confirm trip deletion\n    const confirmDeleteTrip = (item)=>{\n        if (item) {\n            setDeleteTripItem(item);\n            setDeleteTripConfirmation(true);\n        }\n    };\n    const [deleteTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.DeleteTripReport_LogBookEntrySections, {\n        onCompleted: ()=>{\n            // const data = response.deleteTripReport_LogBookEntrySection\n            // updateTripReport({\n            //     id: data,\n            //     key: 'archived',\n            //     value: true,\n            // })\n            updateTripReport({\n                id: [\n                    ...tripReport.filter((trip)=>trip.id !== deleteTripItem.id).map((trip)=>trip.id)\n                ]\n            });\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting trip report\", error);\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    });\n    const handleDeleteTrip = async ()=>{\n        if (deleteTripItem) {\n            if (!offline) {\n                await deleteTripReport_LogBookEntrySection({\n                    variables: {\n                        ids: [\n                            deleteTripItem.id\n                        ]\n                    }\n                });\n            } else {\n                await tripReportModel.save({\n                    id: deleteTripItem.id,\n                    archived: true\n                });\n            }\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: allowedVesselTypes.includes(vessel.vesselType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex xs:flex-row flex-col xs:justify-between xs:gap-2 xs:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_20__.Combobox, {\n                            options: tripScheduleServices,\n                            value: tabTSS ? tripScheduleServices.find((option)=>option.value === tabTSS) || null : tripScheduleServices.find((option)=>option.value === selectedTripScheduleServiceID) || null,\n                            onChange: (e)=>{\n                                setTabTSS((e === null || e === void 0 ? void 0 : e.value) || \"\");\n                                if (e) {\n                                    setSelectedTripScheduleServiceID(e.value);\n                                    loadTripReportSchedules(e.value);\n                                } else {\n                                    setSelectedTripScheduleServiceID(null);\n                                    setTripReportSchedules([]);\n                                    setShowNextTrips(false);\n                                }\n                            },\n                            placeholder: \"Select Trip Schedule Service\",\n                            disabled: locked\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1271,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                            onClick: handleCustomTrip,\n                            variant: \"primary\",\n                            disabled: locked,\n                            children: \"Add Non-Scheduled Trip\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1299,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1270,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1268,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: \"This section covers the logbook entry. This can be made up of a single trip or many over the course of the voyage.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1331,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: combinedTripData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        hasPastScheduledTrips && !showPastScheduledTrips && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                onClick: ()=>setShowPastScheduledTrips(true),\n                                variant: \"primary\",\n                                children: \"Show Past Trips\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 1342,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1341,\n                            columnNumber: 29\n                        }, this),\n                        showPastScheduledTrips && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                onClick: ()=>setShowPastScheduledTrips(false),\n                                variant: \"primary\",\n                                children: \"Hide Past Trips\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 1355,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1354,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.Accordion, {\n                            type: \"single\",\n                            collapsible: true,\n                            value: accordionValue,\n                            onValueChange: (value)=>{\n                                setAccordionValue(value);\n                                // If we're closing the accordion, reset the state\n                                if (value === \"\") {\n                                    setSelectedTab(0);\n                                    setOpenTripModal(false);\n                                    setCurrentTrip(false);\n                                    setSelectedTripReportSchedule(null);\n                                } else {\n                                    // Find the selected item from combined data\n                                    const selectedItem = combinedTripData.find((item)=>item.id.toString() === value);\n                                    if (selectedItem) {\n                                        if (selectedItem.isScheduled && !selectedItem.isCreated) {\n                                            // This is a scheduled trip that hasn't been created yet\n                                            // Don't set currentTrip, just expand the accordion\n                                            setSelectedTab(0);\n                                            setOpenTripModal(false);\n                                            setCurrentTrip(false);\n                                            setSelectedTripReportSchedule(null);\n                                        } else {\n                                            // This is an existing trip or a created scheduled trip\n                                            setSelectedTab(selectedItem.id);\n                                            setOpenTripModal(true);\n                                            setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default()());\n                                            currentTripRef.current = selectedItem;\n                                            setCurrentTrip(selectedItem);\n                                            setSelectedTripReportSchedule(null);\n                                            // Initialize signature data if available\n                                            if (selectedItem.sectionSignature) {\n                                                setSignature(selectedItem.sectionSignature.signatureData || \"\");\n                                            } else {\n                                                setSignature(\"\");\n                                            }\n                                            setRiskBufferEvDgr(selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.dangerousGoodsChecklist);\n                                            setOpenTripStartRiskAnalysis(false);\n                                            setAllDangerousGoods(false);\n                                            setCurrentStopEvent(false);\n                                            setCurrentEventTypeEvent(false);\n                                            setSelectedRowEvent(false);\n                                            setDisplayDangerousGoods((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.enableDGR) === true);\n                                            setDisplayDangerousGoodsSailing((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.designatedDangerousGoodsSailing) === true);\n                                            setDisplayDangerousGoodsPvpd(false);\n                                            setDisplayDangerousGoodsPvpdSailing(null);\n                                            setAllPVPDDangerousGoods(false);\n                                            setSelectedDGRPVPD(false);\n                                            setTripReport_Stops(false);\n                                        }\n                                    }\n                                }\n                            },\n                            children: combinedTripData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionItem, {\n                                    value: item.id.toString(),\n                                    id: \"combined-trip-\".concat(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-2 min-h-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row sm:items-center xs:gap-x-2 flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm sm:text-base font-medium truncate\",\n                                                                dangerouslySetInnerHTML: {\n                                                                    __html: item.displayText\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1451,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            item.isScheduled && !item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded whitespace-nowrap self-start sm:self-auto\",\n                                                                children: \"Scheduled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1459,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            !item.isScheduled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap xs:gap-x-2 text-xs sm:text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"whitespace-nowrap\",\n                                                                        children: [\n                                                                            \"POB: \",\n                                                                            item.totalPOB\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1465,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"whitespace-nowrap\",\n                                                                        children: [\n                                                                            \"Total Pax Carried:\",\n                                                                            \" \",\n                                                                            item.totalPaxJoined\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1468,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"whitespace-nowrap\",\n                                                                        children: [\n                                                                            \"Total Vehicles Carried:\",\n                                                                            \" \",\n                                                                            item.totalVehiclesJoined\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1474,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1464,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1450,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 self-end sm:self-auto\",\n                                                        children: [\n                                                            !item.isScheduled && item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleCreateFromSchedule(item);\n                                                                },\n                                                                size: \"sm\",\n                                                                disabled: locked,\n                                                                children: \"Create Trip\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1487,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            !item.isScheduled && !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        confirmDeleteTrip(item);\n                                                                    },\n                                                                    iconLeft: _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"],\n                                                                    size: \"sm\",\n                                                                    variant: \"destructive\",\n                                                                    disabled: locked,\n                                                                    children: \"Delete Trip\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                    lineNumber: 1502,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            }, void 0, false)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1484,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1449,\n                                                columnNumber: 41\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1448,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionContent, {\n                                            className: \"px-5 sm:px-10\",\n                                            children: !item.isScheduled || item.isCreated ? currentTrip && currentTrip.id === item.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TripReportAccordionContent, {\n                                                currentTrip: currentTrip,\n                                                offline: offline,\n                                                tripReport: tripReport,\n                                                updateTripReport: updateTripReport,\n                                                updateTripReport_LogBookEntrySection: updateTripReport_LogBookEntrySection,\n                                                currentTripRef: currentTripRef,\n                                                locations: locations,\n                                                locked: locked,\n                                                edit_tripReport: edit_tripReport,\n                                                vessel: vessel,\n                                                crewMembers: crewMembers,\n                                                logBookConfig: logBookConfig,\n                                                client: client,\n                                                canCarryVehicles: canCarryVehicles,\n                                                canCarryDangerousGoods: canCarryDangerousGoods,\n                                                selectedDGR: selectedDGR,\n                                                displayDangerousGoods: displayDangerousGoods,\n                                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                                allDangerousGoods: allDangerousGoods,\n                                                setAllDangerousGoods: setAllDangerousGoods,\n                                                logBookStartDate: logBookStartDate,\n                                                masterID: masterID,\n                                                vessels: vessels,\n                                                setSelectedRowEvent: setSelectedRowEvent,\n                                                setCurrentEventTypeEvent: setCurrentEventTypeEvent,\n                                                setCurrentStopEvent: setCurrentStopEvent,\n                                                currentEventTypeEvent: currentEventTypeEvent,\n                                                currentStopEvent: currentStopEvent,\n                                                tripReport_Stops: tripReport_Stops,\n                                                setTripReport_Stops: setTripReport_Stops,\n                                                displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                                                setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                                                displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                                                setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                selectedDGRPVPD: selectedDGRPVPD,\n                                                setSelectedDGRPVPD: setSelectedDGRPVPD,\n                                                fuelLogs: fuelLogs,\n                                                comment: comment,\n                                                setComment: setComment,\n                                                displayFieldTripLog: displayFieldTripLog,\n                                                signatureKey: signatureKey,\n                                                signature: signature,\n                                                setSignature: setSignature,\n                                                handleCancel: handleCancel,\n                                                handleSave: handleSave\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1551,\n                                                columnNumber: 49\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: 'Click the \"Create Trip\" button above to create this scheduled trip and access the trip details.'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                    lineNumber: 1665,\n                                                    columnNumber: 49\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1664,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1546,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, \"combined-trip-\".concat(item.id, \"-\").concat(item.isScheduled ? \"scheduled\" : \"existing\"), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1444,\n                                    columnNumber: 33\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1365,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1336,\n                columnNumber: 13\n            }, this),\n            !allowedVesselTypes.includes(vessel.vesselType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                    onClick: handleCustomTrip,\n                    variant: \"primary\",\n                    disabled: locked,\n                    children: \"Add Trip\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1683,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1682,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__.AlertDialogNew, {\n                openDialog: deleteTripConfirmation,\n                setOpenDialog: setDeleteTripConfirmation,\n                // handleCreate={handleDeleteTrip}\n                title: \"Delete Trip\",\n                description: \"Are you sure you want to delete this trip? This action cannot be undone.\",\n                cancelText: \"Cancel\",\n                destructiveActionText: \"Delete\",\n                handleDestructiveAction: handleDeleteTrip,\n                showDestructiveAction: true,\n                variant: \"danger\",\n                actionText: \"Delete\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1693,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TripLog, \"UflxCJlHZ2oH3JBKGICvXge8038=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        nuqs__WEBPACK_IMPORTED_MODULE_42__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_42__.useQueryState,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_40__.useBreakpoints,\n        _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_39__.useResponsiveLabel,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation\n    ];\n});\n_c1 = TripLog;\nvar _c, _c1;\n$RefreshReg$(_c, \"TripReportAccordionContent\");\n$RefreshReg$(_c1, \"TripLog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx\n"));

/***/ })

});