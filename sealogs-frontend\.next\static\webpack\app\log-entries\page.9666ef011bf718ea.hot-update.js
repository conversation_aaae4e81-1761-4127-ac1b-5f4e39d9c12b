"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/logbook/trip-log.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripLog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/eventType */ \"(app-pages-browser)/./src/app/offline/models/eventType.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _depart_time__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./depart-time */ \"(app-pages-browser)/./src/app/ui/logbook/depart-time.tsx\");\n/* harmony import */ var _exp_arrival_time__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./exp-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/exp-arrival-time.tsx\");\n/* harmony import */ var _actual_arrival_time__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./actual-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./events */ \"(app-pages-browser)/./src/app/ui/logbook/events.tsx\");\n/* harmony import */ var _trip_log_pob__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./trip-log-pob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx\");\n/* harmony import */ var _trip_log_vob__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./trip-log-vob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-vob.tsx\");\n/* harmony import */ var _trip_log_dgr__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./trip-log-dgr */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-dgr.tsx\");\n/* harmony import */ var _components_trip_comments__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./components/trip-comments */ \"(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_master__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./components/master */ \"(app-pages-browser)/./src/app/ui/logbook/components/master.tsx\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React and Next.js imports\n\n\n\n\n\n\n// Utility imports\n\n\n\n\n\n\n\n// Model imports\n\n\n\n\n\n// UI Component imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Reusable TripReportAccordionContent component\nconst TripReportAccordionContent = (param)=>{\n    let { currentTrip, offline, tripReport, updateTripReport, updateTripReport_LogBookEntrySection, currentTripRef, locations, locked, edit_tripReport, vessel, crewMembers, logBookConfig, client, canCarryVehicles, canCarryDangerousGoods, selectedDGR, displayDangerousGoods, setDisplayDangerousGoods, displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing, allDangerousGoods, setAllDangerousGoods, logBookStartDate, masterID, vessels, setSelectedRowEvent, setCurrentEventTypeEvent, setCurrentStopEvent, currentEventTypeEvent, currentStopEvent, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, comment, setComment, displayFieldTripLog, signatureKey, signature, setSignature, handleCancel, handleSave } = param;\n    var _currentTripRef_current;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"space-y-6\", locked || !edit_tripReport ? \"opacity-70 pointer-events-none\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col inset-y-0 items-center pt-10 pb-4 absolute -left-5 sm:-left-[25px] min-h-24 w-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"border-l h-full border-wedgewood-200 border-dashed\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_35__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_depart_time__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        templateStyle: \"\",\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                        label: \"Departure location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLocation)=>{\n                                // Update the from location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"fromLocationID\",\n                                        value: selectedLocation.value,\n                                        label: selectedLocation.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                fromLocationID: selectedLocation.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_18__.H5, {\n                        children: \"PEOPLE ON BOARD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_pob__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        vessel: vessel,\n                        crewMembers: crewMembers,\n                        logBookConfig: logBookConfig,\n                        masterTerm: client === null || client === void 0 ? void 0 : client.masterTerm,\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            canCarryVehicles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_vob__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                offline: offline,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 25\n                            }, undefined),\n                            canCarryDangerousGoods && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_dgr__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                offline: offline,\n                                locked: locked || !edit_tripReport,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig,\n                                selectedDGR: selectedDGR,\n                                members: crewMembers,\n                                displayDangerousGoods: displayDangerousGoods,\n                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                allDangerousGoods: allDangerousGoods,\n                                setAllDangerousGoods: setAllDangerousGoods\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_events__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        offline: offline,\n                        logBookStartDate: logBookStartDate,\n                        currentTrip: currentTrip,\n                        logBookConfig: logBookConfig,\n                        updateTripReport: updateTripReport,\n                        locked: locked,\n                        geoLocations: locations,\n                        tripReport: tripReport,\n                        crewMembers: crewMembers,\n                        masterID: masterID,\n                        vessel: vessel,\n                        vessels: vessels,\n                        setSelectedRow: setSelectedRowEvent,\n                        setCurrentEventType: setCurrentEventTypeEvent,\n                        setCurrentStop: setCurrentStopEvent,\n                        currentEventType: currentEventTypeEvent,\n                        currentStop: currentStopEvent,\n                        tripReport_Stops: tripReport_Stops,\n                        setTripReport_Stops: setTripReport_Stops,\n                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                        selectedDGRPVPD: selectedDGRPVPD,\n                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                        fuelLogs: fuelLogs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                        label: \"Arrival location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLoc)=>{\n                                // Update the to location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"toLocationID\",\n                                        value: selectedLoc.value,\n                                        label: selectedLoc.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                toLocationID: selectedLoc.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 133,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_exp_arrival_time__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 342,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_actual_arrival_time__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 349,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trip_comments__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                setCommentField: setComment,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 356,\n                columnNumber: 13\n            }, undefined),\n            displayFieldTripLog(\"MasterID\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked || !edit_tripReport ? \"pointer-events-none\" : \"\", \" max-w-sm\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_master__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                    offline: offline,\n                    currentTrip: currentTrip,\n                    tripReport: tripReport,\n                    crewMembers: crewMembers,\n                    updateTripReport: updateTripReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 364,\n                columnNumber: 17\n            }, undefined),\n            displayFieldTripLog(\"Signature\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                locked: locked,\n                title: \"Signature Confirmation\",\n                description: \"By signing below, I confirm that the recorded entries are accurate to the best of my knowledge and in accordance with the vessel's operating procedures and regulations.\",\n                signature: currentTripRef === null || currentTripRef === void 0 ? void 0 : (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.sectionSignature,\n                onSignatureChanged: (sign)=>{\n                    setSignature(sign);\n                }\n            }, \"\".concat(signatureKey, \"-\").concat(currentTrip.id), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 377,\n                columnNumber: 17\n            }, undefined),\n            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_36__.FormFooter, {\n                className: \"justify-end flex-nowrap xs:gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        variant: \"back\",\n                        onClick: handleCancel,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        iconLeft: _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"],\n                        onClick: handleSave,\n                        children: \"Update\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 390,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n        lineNumber: 126,\n        columnNumber: 9\n    }, undefined);\n};\n_c = TripReportAccordionContent;\nfunction TripLog(param) {\n    let { tripReport = false, logBookConfig, updateTripReport, locked, crewMembers, masterID, createdTab = false, setCreatedTab, currentTrip = false, setCurrentTrip, vessels, offline = false, fuelLogs, logBookStartDate } = param;\n    var _tripReport_find;\n    _s();\n    // const router = useRouter()\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const allowedVesselTypes = [\n        \"SLALL\",\n        \"Tug_Boat\",\n        \"Passenger_Ferry\",\n        \"Water_Taxi\"\n    ];\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_42__.useQueryState)(\"tab\", {\n        defaultValue: \"crew\"\n    });\n    const [tabTSS, setTabTSS] = (0,nuqs__WEBPACK_IMPORTED_MODULE_42__.useQueryState)(\"tss\", {\n        defaultValue: \"\"\n    });\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eventTypes, setEventTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openTripModal, setOpenTripModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bufferTripID, setBufferTripID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDGR, setSelectedDGR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoods, setDisplayDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [openEventModal, setOpenEventModal] = useState(false)\n    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)\n    const [tripReport_Stops, setTripReport_Stops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDGRPVPD, setSelectedDGRPVPD] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRowEvent, setSelectedRowEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [riskBufferEvDgr, setRiskBufferEvDgr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDangerousGoods, setAllDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEventTypeEvent, setCurrentEventTypeEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStopEvent, setCurrentStopEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signatureKey, setSignatureKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default()());\n    const currentTripRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [deleteTripItem, setDeleteTripItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteTripConfirmation, setDeleteTripConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canCarryDangerousGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesDangerousGoods);\n    }, [\n        vessel\n    ]);\n    const canCarryVehicles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesVehicles);\n    }, [\n        vessel\n    ]);\n    // Initialize client\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_10__.getOneClient)(setClient);\n    }\n    // Update signature state when currentTrip changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentTrip && currentTrip.sectionSignature) {\n            setSignature(currentTrip.sectionSignature.signatureData || \"\");\n        } else {\n            setSignature(\"\");\n        }\n    }, [\n        currentTrip\n    ]);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(tripReport ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.comment : \"\");\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_tripReport, setEdit_tripReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const clientModel = new _app_offline_models_client__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const eventTypeModel = new _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_16__[\"default\"]();\n    const [openTripSelectionDialog, setOpenTripSelectionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tripReportSchedules, setTripReportSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripReportSchedule, setSelectedTripReportSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tripScheduleServices, setTripScheduleServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNextTrips, setShowNextTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreatingScheduledTrip, setIsCreatingScheduledTrip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPastScheduledTrips, setShowPastScheduledTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Responsive hooks\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_40__.useBreakpoints)();\n    const responsiveLabel = (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_39__.useResponsiveLabel)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"EDIT_LOGBOOKENTRY_TRIPREPORT\", permissions)) {\n                setEdit_tripReport(true);\n            } else {\n                setEdit_tripReport(false);\n            }\n        }\n    };\n    const offlineLoad = async ()=>{\n        const locations = await geoLocationModel.getAll();\n        setLocations(locations);\n        const types = await eventTypeModel.getAll();\n        setEventTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.getPermissions);\n        loadTripScheduleServices();\n        if (!locations) {\n            if (offline) {\n                offlineLoad();\n            } else {\n                loadLocations();\n                loadEventTypes();\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (createdTab) {\n            setSelectedTab(createdTab);\n        }\n    }, [\n        createdTab\n    ]);\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_10__.getVesselByID)(+vesselID, setVessel);\n    }\n    const scrollToAccordionItem = (tripId)=>{\n        const element = document.getElementById(\"triplog-\".concat(tripId));\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport && currentTrip) {\n            const trip = tripReport.find((trip)=>trip.id === currentTrip.id);\n            currentTripRef.current = trip;\n            setCurrentTrip(trip);\n        }\n        if (tripReport && bufferTripID > 0) {\n            const trip = tripReport.find((trip)=>trip.id === bufferTripID);\n            if (trip) {\n                currentTripRef.current = trip;\n                setCurrentTrip(trip);\n                // Only expand accordion and scroll for regular trips, not scheduled trips\n                if (!selectedTripReportSchedule) {\n                    setAccordionValue(trip.id.toString());\n                    scrollToAccordionItem(trip.id);\n                }\n                setOpenTripModal(true);\n                setSelectedTab(trip.id);\n                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default()());\n                // Initialize signature data if available\n                if (trip.sectionSignature) {\n                    setSignature(trip.sectionSignature.signatureData || \"\");\n                } else {\n                    setSignature(\"\");\n                }\n                // Initialize trip-specific state\n                setRiskBufferEvDgr(trip === null || trip === void 0 ? void 0 : trip.dangerousGoodsChecklist);\n                setOpenTripStartRiskAnalysis(false);\n                setAllDangerousGoods(false);\n                setCurrentStopEvent(false);\n                setCurrentEventTypeEvent(false);\n                setSelectedRowEvent(false);\n                setDisplayDangerousGoods((trip === null || trip === void 0 ? void 0 : trip.enableDGR) === true);\n                setDisplayDangerousGoodsSailing((trip === null || trip === void 0 ? void 0 : trip.designatedDangerousGoodsSailing) === true);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setAllPVPDDangerousGoods(false);\n                setSelectedDGRPVPD(false);\n                setTripReport_Stops(false);\n            }\n            setBufferTripID(0);\n        }\n    }, [\n        tripReport\n    ]);\n    const [loadLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setLocations(response.readGeoLocations.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading locations\", error);\n        }\n    });\n    const [loadEventTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_EVENT_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setEventTypes(response.readEventTypes.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading activity types\", error);\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_Stop, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const handleCreateTripReportScheduleStops = async (logBookEntrySectionID)=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(selectedTripReportSchedule)) {\n            const tripStops = selectedTripReportSchedule.tripReportScheduleStops.nodes || [];\n            await Promise.all(tripStops.map(async (stop)=>{\n                const input = {\n                    logBookEntrySectionID: logBookEntrySectionID,\n                    tripReportScheduleStopID: stop.id,\n                    arriveTime: stop.arriveTime,\n                    departTime: stop.departTime,\n                    stopLocationID: stop.stopLocationID\n                };\n                await createTripReport_Stop({\n                    variables: {\n                        input: input\n                    }\n                });\n            }));\n            setSelectedTripReportSchedule(null);\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        logBookEntrySectionID\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        logBookEntrySectionID\n                    ]\n                });\n            }\n        }\n    };\n    const [createTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            handleCreateTripReportScheduleStops(data.id);\n            // Always set accordion value and scroll for newly created trips\n            setAccordionValue(data.id.toString());\n            scrollToAccordionItem(data.id);\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n            setCurrentTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [readTripReportSchedules, { loading: readTripReportSchedulesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripReportSchedules, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripReportSchedules.nodes.filter((trip)=>// only show trips for the current vessel\n                trip.vehicles.nodes.some((vehicle)=>+vehicle.id === +vesselID));\n            if (showNextTrips) {\n                // only show 1 past trip and 4 upcoming trips\n                const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n                const pastIndex = data.findIndex((trip)=>trip.departTime >= currentTime);\n                const result = (pastIndex > 0 ? [\n                    data[pastIndex - 1]\n                ] : []).concat(data.slice(pastIndex, pastIndex + 4));\n                setTripReportSchedules(result);\n            } else {\n                setTripReportSchedules(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripReportSchedules\", error);\n        }\n    });\n    const [readTripScheduleServices] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripScheduleServices, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripScheduleServices.nodes.map((tss)=>{\n                return {\n                    label: tss.title,\n                    value: tss.id\n                };\n            });\n            setTripScheduleServices(data);\n            setTripReportSchedules([]);\n        // setOpenTripSelectionDialog(true)\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripScheduleServices\", error);\n        }\n    });\n    const loadTripScheduleServices = async ()=>{\n        await readTripScheduleServices({\n            variables: {\n                filter: {\n                    vehicles: {\n                        id: {\n                            eq: vesselID\n                        }\n                    }\n                }\n            }\n        });\n    };\n    const loadTripReportSchedules = async (tripScheduleServiceID)=>{\n        setTripReportSchedules([]);\n        await readTripReportSchedules({\n            variables: {\n                filter: {\n                    // archived: { eq: false },\n                    // start: { eq: logBookStartDate },\n                    tripScheduleServiceID: {\n                        eq: tripScheduleServiceID\n                    }\n                }\n            }\n        });\n    };\n    const doCreateTripReport = async function(input) {\n        let isScheduledTrip = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!edit_tripReport) {\n            sonner__WEBPACK_IMPORTED_MODULE_17__.toast.error(\"You do not have permission to add a trip\");\n            return;\n        }\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setIsCreatingScheduledTrip(isScheduledTrip);\n        if (offline) {\n            const data = await tripReportModel.save({\n                ...input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_11__.generateUniqueId)()\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            // For offline mode, immediately set accordion value since the trip is created synchronously\n            setAccordionValue(data.id.toString());\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        } else {\n            createTripReport_LogBookEntrySection({\n                variables: {\n                    input: input\n                }\n            });\n        }\n        setRiskBufferEvDgr(false);\n        setOpenTripStartRiskAnalysis(false);\n        setAllDangerousGoods(false);\n        setCurrentStopEvent(false);\n        setCurrentEventTypeEvent(false);\n        setSelectedRowEvent(false);\n        setDisplayDangerousGoods(false);\n        setDisplayDangerousGoodsSailing(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        setAllPVPDDangerousGoods(false);\n        setSelectedDGRPVPD(false);\n        setTripReport_Stops(false);\n    };\n    const handleCustomTrip = ()=>{\n        setOpenTripSelectionDialog(false);\n        const input = {\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, false) // false indicates this is a regular trip\n        ;\n    };\n    // Removed unused handleEditTrip function\n    const [createLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: +data.logBookEntrySectionID,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error saving signature\", error);\n        }\n    });\n    const [updateLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            var _currentTripRef_current;\n            const data = response.updateLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"341 TripLog updateLogBookEntrySection_Signature\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        // Log signature information for debugging\n        // Ensure we have a valid signature\n        const sigVariables = {\n            logBookEntrySectionID: currentTrip.id,\n            memberID: localStorage.getItem(\"userId\"),\n            signatureData: signature || \"\"\n        };\n        if (+currentTrip.sectionSignatureID > 0) {\n            // Update signature\n            updateLogBookEntrySection_Signature({\n                variables: {\n                    input: {\n                        ...sigVariables,\n                        id: +currentTrip.sectionSignatureID\n                    }\n                }\n            });\n        } else {\n            // Create signature\n            createLogBookEntrySection_Signature({\n                variables: {\n                    input: sigVariables !== null && sigVariables !== void 0 ? sigVariables : \"\"\n                }\n            });\n        }\n        if (offline) {\n            // updateTripReport_LogBookEntrySection\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                comment: comment || null\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n        } else {\n            var _currentTripRef_current;\n            await updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        comment: comment || null\n                    }\n                }\n            });\n            setOpenTripModal(false);\n            setCurrentTrip(false);\n        }\n    };\n    const displayFieldTripLog = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"TripReport_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const convertTimeFormat = (time)=>{\n        if (time === null || time === undefined) return \"\";\n        const [hours, minutes] = time.split(\":\");\n        return \"\".concat(hours, \":\").concat(minutes);\n    };\n    const handleCancel = ()=>{\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setAccordionValue(\"\");\n    };\n    // Removed unused functions\n    const initOffline = async ()=>{\n        var _localStorage_getItem;\n        // getOneClient\n        const client = await clientModel.getById((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n        setClient(client);\n        // getVesselByID(+vesselID, setVessel)\n        const vessel = await vesselModel.getById(vesselID);\n        setVessel(vessel);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            initOffline();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_7___default()(tabTSS))) {\n            loadTripReportSchedules(tabTSS);\n        }\n    }, [\n        tabTSS\n    ]);\n    /**\r\n     * Takes an array of trip report schedules and returns an array of the latest unique schedules based on:\r\n     * - departTime\r\n     * - arriveTime\r\n     * - fromLocationID\r\n     * - toLocationID\r\n     * - start\r\n     *\r\n     * Schedules with the same combination of the above properties will be deduplicated, with the latest schedule (based on id) being kept.\r\n     *\r\n     * @param {TripReportSchedule[]} schedules - An array of trip report schedules\r\n     * @returns {TripReportSchedule[]} - An array of the latest unique trip report schedules\r\n     */ const getLatestUniqueSchedules = (schedules)=>{\n        const map = new Map();\n        for (const schedule of schedules){\n            const key = \"\".concat(schedule.departTime, \"|\").concat(schedule.arriveTime, \"|\").concat(schedule.fromLocationID, \"|\").concat(schedule.toLocationID, \"|\").concat(schedule.start);\n            const existing = map.get(key);\n            if (!existing || Number(schedule.id) > Number(existing.id)) {\n                map.set(key, schedule);\n            }\n        }\n        return Array.from(map.values());\n    };\n    // Create combined trip data that merges tripReport and tripReportSchedules\n    const combinedTripData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const existingTrips = (tripReport || []).filter((trip)=>!(trip === null || trip === void 0 ? void 0 : trip.archived)).map((trip)=>{\n            var _trip_fromLocation, _trip_fromLocation1, _trip_toLocation, _trip_toLocation1, _trip_fromLocation2, _trip_toLocation2;\n            return {\n                ...trip,\n                isScheduled: false,\n                isCreated: true,\n                sortTime: trip.departTime || \"00:00:00\",\n                displayText: \"<div>\".concat((trip === null || trip === void 0 ? void 0 : trip.departTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.departTime) + \" - \" : \"No depart time - \").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation = trip.fromLocation) === null || _trip_fromLocation === void 0 ? void 0 : _trip_fromLocation.title) || \"\").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation1 = trip.fromLocation) === null || _trip_fromLocation1 === void 0 ? void 0 : _trip_fromLocation1.title) && (trip === null || trip === void 0 ? void 0 : (_trip_toLocation = trip.toLocation) === null || _trip_toLocation === void 0 ? void 0 : _trip_toLocation.title) ? \" -> \" : \"\", '<span class=\"').concat((0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_38__.isVesselArrivalLate)(trip === null || trip === void 0 ? void 0 : trip.arriveTime, trip === null || trip === void 0 ? void 0 : trip.arrive) ? \"text-cinnabar-500\" : \"\", '\">').concat((trip === null || trip === void 0 ? void 0 : trip.arrive) ? convertTimeFormat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(trip === null || trip === void 0 ? void 0 : trip.arrive).format(\"HH:mm \") + \" - \") : (trip === null || trip === void 0 ? void 0 : trip.arriveTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.arriveTime) + \" - \" : \" No arrival time \", \"</span> \").concat((trip === null || trip === void 0 ? void 0 : (_trip_toLocation1 = trip.toLocation) === null || _trip_toLocation1 === void 0 ? void 0 : _trip_toLocation1.title) || \"\").concat(!(trip === null || trip === void 0 ? void 0 : (_trip_fromLocation2 = trip.fromLocation) === null || _trip_fromLocation2 === void 0 ? void 0 : _trip_fromLocation2.title) && !(trip === null || trip === void 0 ? void 0 : (_trip_toLocation2 = trip.toLocation) === null || _trip_toLocation2 === void 0 ? void 0 : _trip_toLocation2.title) ? \" \" : \" \", \"</div>\")\n            };\n        });\n        const latestUniqueSchedules = getLatestUniqueSchedules(tripReportSchedules);\n        // Get current time for filtering\n        const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n        const scheduledTrips = (latestUniqueSchedules || []).map((schedule)=>{\n            // Check if this schedule has already been created as a trip\n            const isAlreadyCreated = existingTrips.some((trip)=>trip.tripReportScheduleID === schedule.id);\n            return {\n                ...schedule,\n                isScheduled: true,\n                isCreated: isAlreadyCreated,\n                sortTime: schedule.departTime || \"00:00:00\",\n                displayText: \"\".concat(schedule.departTime, \" - \").concat(schedule.arriveTime, \" | \").concat(schedule.fromLocation.title, \" → \").concat(schedule.toLocation.title)\n            };\n        });\n        // Filter scheduled trips based on time and showPastScheduledTrips state\n        const filteredScheduledTrips = scheduledTrips.filter((s)=>{\n            if (s.isCreated) return false // Don't show already created trips\n            ;\n            const tripTime = s.sortTime || \"00:00:00\";\n            const isUpcoming = tripTime >= currentTime;\n            // Show upcoming trips by default, past trips only when showPastScheduledTrips is true\n            return showPastScheduledTrips ? !isUpcoming : isUpcoming;\n        });\n        // Combine and sort by departure time\n        const combined = [\n            ...existingTrips,\n            ...filteredScheduledTrips\n        ];\n        return combined.sort((a, b)=>{\n            const timeA = a.sortTime || \"00:00:00\";\n            const timeB = b.sortTime || \"00:00:00\";\n            return timeA.localeCompare(timeB);\n        });\n    }, [\n        tripReport,\n        tripReportSchedules,\n        showPastScheduledTrips\n    ]);\n    // Check if there are past scheduled trips available\n    const hasPastScheduledTrips = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n        const latestUniqueSchedules = getLatestUniqueSchedules(tripReportSchedules);\n        const existingTrips = tripReport || [];\n        return latestUniqueSchedules.some((schedule)=>{\n            const isAlreadyCreated = existingTrips.some((trip)=>trip.tripReportScheduleID === schedule.id);\n            const tripTime = schedule.departTime || \"00:00:00\";\n            const isPast = tripTime < currentTime;\n            return !isAlreadyCreated && isPast;\n        });\n    }, [\n        tripReport,\n        tripReportSchedules\n    ]);\n    // Handle creating a trip from a scheduled item\n    const handleCreateFromSchedule = (scheduleItem)=>{\n        setSelectedTripReportSchedule(scheduleItem);\n        const input = {\n            tripReportScheduleID: scheduleItem.id,\n            departTime: scheduleItem.departTime,\n            arriveTime: scheduleItem.arriveTime,\n            fromLocationID: scheduleItem.fromLocationID,\n            toLocationID: scheduleItem.toLocationID,\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, true) // true indicates this is a scheduled trip\n        ;\n    };\n    // Implement confirmDeletetrip using  AlertNew component to confirm trip deletion\n    const confirmDeleteTrip = (item)=>{\n        if (item) {\n            setDeleteTripItem(item);\n            setDeleteTripConfirmation(true);\n        }\n    };\n    const [deleteTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.DeleteTripReport_LogBookEntrySections, {\n        onCompleted: ()=>{\n            // const data = response.deleteTripReport_LogBookEntrySection\n            // updateTripReport({\n            //     id: data,\n            //     key: 'archived',\n            //     value: true,\n            // })\n            updateTripReport({\n                id: [\n                    ...tripReport.filter((trip)=>trip.id !== deleteTripItem.id).map((trip)=>trip.id)\n                ]\n            });\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting trip report\", error);\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    });\n    const handleDeleteTrip = async ()=>{\n        if (deleteTripItem) {\n            if (!offline) {\n                await deleteTripReport_LogBookEntrySection({\n                    variables: {\n                        ids: [\n                            deleteTripItem.id\n                        ]\n                    }\n                });\n            } else {\n                await tripReportModel.save({\n                    id: deleteTripItem.id,\n                    archived: true\n                });\n            }\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: allowedVesselTypes.includes(vessel.vesselType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex xs:flex-row flex-col xs:justify-between gap-2 xs:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_20__.Combobox, {\n                            options: tripScheduleServices,\n                            value: tabTSS ? tripScheduleServices.find((option)=>option.value === tabTSS) || null : tripScheduleServices.find((option)=>option.value === selectedTripScheduleServiceID) || null,\n                            onChange: (e)=>{\n                                setTabTSS((e === null || e === void 0 ? void 0 : e.value) || \"\");\n                                if (e) {\n                                    setSelectedTripScheduleServiceID(e.value);\n                                    loadTripReportSchedules(e.value);\n                                } else {\n                                    setSelectedTripScheduleServiceID(null);\n                                    setTripReportSchedules([]);\n                                    setShowNextTrips(false);\n                                }\n                            },\n                            placeholder: \"Select Trip Schedule Service\",\n                            disabled: locked\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1271,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                            onClick: handleCustomTrip,\n                            variant: \"primary\",\n                            disabled: locked,\n                            children: \"Add Non-Scheduled Trip\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1299,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1270,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1268,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: \"This section covers the logbook entry. This can be made up of a single trip or many over the course of the voyage.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1331,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: combinedTripData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        hasPastScheduledTrips && !showPastScheduledTrips && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                onClick: ()=>setShowPastScheduledTrips(true),\n                                variant: \"primary\",\n                                children: \"Show Past Trips\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 1342,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1341,\n                            columnNumber: 29\n                        }, this),\n                        showPastScheduledTrips && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                onClick: ()=>setShowPastScheduledTrips(false),\n                                variant: \"primary\",\n                                children: \"Hide Past Trips\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 1355,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1354,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.Accordion, {\n                            type: \"single\",\n                            collapsible: true,\n                            value: accordionValue,\n                            onValueChange: (value)=>{\n                                setAccordionValue(value);\n                                // If we're closing the accordion, reset the state\n                                if (value === \"\") {\n                                    setSelectedTab(0);\n                                    setOpenTripModal(false);\n                                    setCurrentTrip(false);\n                                    setSelectedTripReportSchedule(null);\n                                } else {\n                                    // Find the selected item from combined data\n                                    const selectedItem = combinedTripData.find((item)=>item.id.toString() === value);\n                                    if (selectedItem) {\n                                        if (selectedItem.isScheduled && !selectedItem.isCreated) {\n                                            // This is a scheduled trip that hasn't been created yet\n                                            // Don't set currentTrip, just expand the accordion\n                                            setSelectedTab(0);\n                                            setOpenTripModal(false);\n                                            setCurrentTrip(false);\n                                            setSelectedTripReportSchedule(null);\n                                        } else {\n                                            // This is an existing trip or a created scheduled trip\n                                            setSelectedTab(selectedItem.id);\n                                            setOpenTripModal(true);\n                                            setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_8___default()());\n                                            currentTripRef.current = selectedItem;\n                                            setCurrentTrip(selectedItem);\n                                            setSelectedTripReportSchedule(null);\n                                            // Initialize signature data if available\n                                            if (selectedItem.sectionSignature) {\n                                                setSignature(selectedItem.sectionSignature.signatureData || \"\");\n                                            } else {\n                                                setSignature(\"\");\n                                            }\n                                            setRiskBufferEvDgr(selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.dangerousGoodsChecklist);\n                                            setOpenTripStartRiskAnalysis(false);\n                                            setAllDangerousGoods(false);\n                                            setCurrentStopEvent(false);\n                                            setCurrentEventTypeEvent(false);\n                                            setSelectedRowEvent(false);\n                                            setDisplayDangerousGoods((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.enableDGR) === true);\n                                            setDisplayDangerousGoodsSailing((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.designatedDangerousGoodsSailing) === true);\n                                            setDisplayDangerousGoodsPvpd(false);\n                                            setDisplayDangerousGoodsPvpdSailing(null);\n                                            setAllPVPDDangerousGoods(false);\n                                            setSelectedDGRPVPD(false);\n                                            setTripReport_Stops(false);\n                                        }\n                                    }\n                                }\n                            },\n                            children: combinedTripData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionItem, {\n                                    value: item.id.toString(),\n                                    id: \"combined-trip-\".concat(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between w-full gap-2 min-h-0 sm:pe-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm sm:text-base lg:text-lg font-medium truncate\",\n                                                                dangerouslySetInnerHTML: {\n                                                                    __html: item.displayText\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1451,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            item.isScheduled && !item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded whitespace-nowrap self-start sm:self-auto\",\n                                                                children: \"Scheduled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1459,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            !item.isScheduled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 xs:gap-2 sm:gap-3 text-xs sm:text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"whitespace-nowrap\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden sm:inline\",\n                                                                                children: responsiveLabel(\"POB\", \"People on Board\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                                lineNumber: 1466,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sm:hidden\",\n                                                                                children: \"POB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                                lineNumber: 1472,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            \": \",\n                                                                            item.totalPOB\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1465,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"whitespace-nowrap\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden lg:inline\",\n                                                                                children: responsiveLabel(\"Total Passengers Carried\", \"Total Passengers Carried\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                                lineNumber: 1478,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden sm:inline lg:hidden\",\n                                                                                children: responsiveLabel(\"Total Pax\", \"Total Pax\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                                lineNumber: 1484,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sm:hidden\",\n                                                                                children: \"Pax\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                                lineNumber: 1490,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            \":\",\n                                                                            \" \",\n                                                                            item.totalPaxJoined\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1477,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"whitespace-nowrap\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden lg:inline\",\n                                                                                children: responsiveLabel(\"Total Vehicles Carried\", \"Total Vehicles Carried\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                                lineNumber: 1499,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden sm:inline lg:hidden\",\n                                                                                children: responsiveLabel(\"Total Vehicles\", \"Total Vehicles\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                                lineNumber: 1505,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sm:hidden\",\n                                                                                children: \"Veh\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                                lineNumber: 1511,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            \":\",\n                                                                            \" \",\n                                                                            item.totalVehiclesJoined\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1498,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1464,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1450,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1 xs:gap-2 self-end lg:self-auto mt-2 lg:mt-0\",\n                                                        children: [\n                                                            !item.isScheduled && item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleCreateFromSchedule(item);\n                                                                },\n                                                                size: \"sm\",\n                                                                className: \"text-xs sm:text-sm whitespace-nowrap\",\n                                                                disabled: locked,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"hidden sm:inline\",\n                                                                        children: \"Create Trip\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1535,\n                                                                        columnNumber: 61\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"sm:hidden\",\n                                                                        children: \"Create\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                        lineNumber: 1538,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1525,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            !item.isScheduled && !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        confirmDeleteTrip(item);\n                                                                    },\n                                                                    iconLeft: _barrel_optimize_names_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"],\n                                                                    size: \"sm\",\n                                                                    className: \"text-xs sm:text-sm whitespace-nowrap\",\n                                                                    variant: \"destructive\",\n                                                                    disabled: locked,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"hidden sm:inline\",\n                                                                            children: \"Delete Trip\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                            lineNumber: 1564,\n                                                                            columnNumber: 65\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"sm:hidden\",\n                                                                            children: \"Delete\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                            lineNumber: 1567,\n                                                                            columnNumber: 65\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                    lineNumber: 1546,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            }, void 0, false)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1522,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1449,\n                                                columnNumber: 41\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1448,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionContent, {\n                                            className: \"px-5 sm:px-10\",\n                                            children: !item.isScheduled || item.isCreated ? currentTrip && currentTrip.id === item.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TripReportAccordionContent, {\n                                                currentTrip: currentTrip,\n                                                offline: offline,\n                                                tripReport: tripReport,\n                                                updateTripReport: updateTripReport,\n                                                updateTripReport_LogBookEntrySection: updateTripReport_LogBookEntrySection,\n                                                currentTripRef: currentTripRef,\n                                                locations: locations,\n                                                locked: locked,\n                                                edit_tripReport: edit_tripReport,\n                                                vessel: vessel,\n                                                crewMembers: crewMembers,\n                                                logBookConfig: logBookConfig,\n                                                client: client,\n                                                canCarryVehicles: canCarryVehicles,\n                                                canCarryDangerousGoods: canCarryDangerousGoods,\n                                                selectedDGR: selectedDGR,\n                                                displayDangerousGoods: displayDangerousGoods,\n                                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                                allDangerousGoods: allDangerousGoods,\n                                                setAllDangerousGoods: setAllDangerousGoods,\n                                                logBookStartDate: logBookStartDate,\n                                                masterID: masterID,\n                                                vessels: vessels,\n                                                setSelectedRowEvent: setSelectedRowEvent,\n                                                setCurrentEventTypeEvent: setCurrentEventTypeEvent,\n                                                setCurrentStopEvent: setCurrentStopEvent,\n                                                currentEventTypeEvent: currentEventTypeEvent,\n                                                currentStopEvent: currentStopEvent,\n                                                tripReport_Stops: tripReport_Stops,\n                                                setTripReport_Stops: setTripReport_Stops,\n                                                displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                                                setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                                                displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                                                setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                selectedDGRPVPD: selectedDGRPVPD,\n                                                setSelectedDGRPVPD: setSelectedDGRPVPD,\n                                                fuelLogs: fuelLogs,\n                                                comment: comment,\n                                                setComment: setComment,\n                                                displayFieldTripLog: displayFieldTripLog,\n                                                signatureKey: signatureKey,\n                                                signature: signature,\n                                                setSignature: setSignature,\n                                                handleCancel: handleCancel,\n                                                handleSave: handleSave\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1601,\n                                                columnNumber: 49\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: 'Click the \"Create Trip\" button above to create this scheduled trip and access the trip details.'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                    lineNumber: 1715,\n                                                    columnNumber: 49\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1714,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1596,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, \"combined-trip-\".concat(item.id, \"-\").concat(item.isScheduled ? \"scheduled\" : \"existing\"), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1444,\n                                    columnNumber: 33\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1365,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1336,\n                columnNumber: 13\n            }, this),\n            !allowedVesselTypes.includes(vessel.vesselType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                    onClick: handleCustomTrip,\n                    variant: \"primary\",\n                    disabled: locked,\n                    children: \"Add Trip\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1733,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1732,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__.AlertDialogNew, {\n                openDialog: deleteTripConfirmation,\n                setOpenDialog: setDeleteTripConfirmation,\n                // handleCreate={handleDeleteTrip}\n                title: \"Delete Trip\",\n                description: \"Are you sure you want to delete this trip? This action cannot be undone.\",\n                cancelText: \"Cancel\",\n                destructiveActionText: \"Delete\",\n                handleDestructiveAction: handleDeleteTrip,\n                showDestructiveAction: true,\n                variant: \"danger\",\n                actionText: \"Delete\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1743,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TripLog, \"UflxCJlHZ2oH3JBKGICvXge8038=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        nuqs__WEBPACK_IMPORTED_MODULE_42__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_42__.useQueryState,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_40__.useBreakpoints,\n        _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_39__.useResponsiveLabel,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_43__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_44__.useMutation\n    ];\n});\n_c1 = TripLog;\nvar _c, _c1;\n$RefreshReg$(_c, \"TripReportAccordionContent\");\n$RefreshReg$(_c1, \"TripLog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx\n"));

/***/ })

});